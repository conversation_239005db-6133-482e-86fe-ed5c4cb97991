package applet

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/emicklei/go-restful/v3"
	"gopkg.in/yaml.v3"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	pkgclients "transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
)

const (
	DifyAPIBaseURL = "http://************"
	//DifyAPIBaseURL    = "https://cloud.dify.ai"
	DifyImportPath      = "/console/api/apps/imports"
	DifyPublishPath     = "/console/api/apps/%s/workflows/publish"
	DifySiteTokenPath   = "/console/api/apps/%s/site"
	DifyCheckDepsPath   = "/console/api/apps/imports/%s/check-dependencies"
	DifyWorkflowBaseURL = "https://************/workflow/"
	//DifyWorkflowBaseURL = "https://cloud.dify.ai/workflow/"

	DifyBearerToken = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMGI2N2Y3ZjYtMjQ5OC00ZjM0LTk0ZDEtMmMwZmI0NDVmMDg0IiwiZXhwIjoxNzg0OTQ4NzE1LCJpc3MiOiJTRUxGX0hPU1RFRCIsInN1YiI6IkNvbnNvbGUgQVBJIFBhc3Nwb3J0In0.HWPgwNIp0ux1ii9AAcM7CSxKXiTQ3I2r63uhd8FWuxI"
	//DifyBearerToken = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.Qbwi1G6neefNJGhDF7GIsmDy--5hVnZmLM_tD68GAGA"

	DifyContentType    = "application/json"
	DifyAppName        = "通过API导入的工作流"
	DifyAppDescription = "这是一个通过API导入的工作流应用"

	ReplacementPluginID  = "langgenius/siliconflow:0.0.20@a0297ff9ba92d57b12efa51dad87bbf68f6556979d2f32ed15fc833a3a1f4f39"
	ReplacementModelName = "Qwen/Qwen3-32B"
	ReplacementProvider  = "langgenius/siliconflow/siliconflow"

	// false使用直连，true使用代理
	USE_PROXY_FOR_DIFY = true
)

func getDifyHttpClient() *pkgclients.HttpClient {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true,
		},
		DisableKeepAlives: true,
	}

	if USE_PROXY_FOR_DIFY {
		proxyURL, err := url.Parse("http://*************:3128")
		if err == nil {
			transport.Proxy = http.ProxyURL(proxyURL)
			fmt.Printf("Using proxy for dify requests: http://*************:3128\n")
		} else {
			fmt.Printf("Failed to parse proxy URL, using direct connection: %v\n", err)
		}
	} else {
		fmt.Printf("Using direct connection for dify requests (proxy disabled)\n")
	}

	httpCli := &http.Client{
		Transport: transport,
		Timeout:   360 * time.Second,
	}

	return &pkgclients.HttpClient{Cli: httpCli}
}

func (r *Resource) DifyImportAndPublish(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	req := &models.DifyImportRequest{}
	if err := request.ReadEntity(req); err != nil {
		helper.ErrorResponse(response, stderr.InvalidParam.Cause(err, "invalid request"))
		return
	}
	if req.YamlContent == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("yaml_content is required"))
		return
	}

	// 决定是否跳过预处理yaml文件
	skipPreprocess := request.QueryParameter("skip_preprocess") == "true"

	var processedYamlContent string
	var err error

	if skipPreprocess {
		processedYamlContent = req.YamlContent
	} else {
		// 预处理YAML内容
		processedYamlContent, err = r.preProcessYamlContent(req.YamlContent)
		if err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, "failed to pre-process yaml content"))
			return
		}
	}

	result, err := r.difyImportAndPublishFlow(ctx, processedYamlContent)
	if err != nil {
		helper.ErrorResponse(response, stderr.Wrap(err, "failed to import and publish dify application"))
		return
	}
	helper.SuccessResponse(response, result)
}

// 分为四步，导入dify应用连、检查依赖、发布、获取站点token
func (r *Resource) difyImportAndPublishFlow(ctx context.Context, yamlContent string) (*models.DifyImportResponse, error) {
	appID, err := r.difyImportApp(ctx, yamlContent)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to import dify app")
	}

	err = r.difyCheckDependencies(ctx, appID)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to check dependencies")
	}

	err = r.difyPublishApp(ctx, appID)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to publish dify app")
	}

	accessToken, err := r.difyGetSiteToken(ctx, appID)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get site token")
	}

	// 生成最终的工作流URL
	workflowURL := DifyWorkflowBaseURL + accessToken

	return &models.DifyImportResponse{
		WorkflowURL: workflowURL,
		AppID:       appID,
		AccessToken: accessToken,
	}, nil
}

func (r *Resource) difyImportApp(ctx context.Context, yamlContent string) (string, error) {
	url := DifyAPIBaseURL + DifyImportPath
	requestBody := &models.DifyImportRequestBody{
		Mode:        "yaml-content",
		YamlContent: yamlContent,
		Name:        DifyAppName,
		Description: DifyAppDescription,
	}
	headers := map[string]string{
		"Authorization": DifyBearerToken,
		"Content-Type":  DifyContentType,
	}

	reqBodyJson, err := json.Marshal(requestBody)
	if err != nil {
		return "", stderr.Wrap(err, "failed to marshal request body")
	}

	httpParam := &pkgclients.HttpParam{
		Method:  "POST",
		Url:     url,
		ReqBody: string(reqBodyJson),
		Header:  headers,
	}

	httpCli := getDifyHttpClient()
	respStr, err := httpCli.HttpCallString(ctx, httpParam)
	if err != nil {
		return "", stderr.Wrap(err, "failed to call dify import API")
	}

	// 解析响应
	var response models.DifyImportResponseBody
	err = json.Unmarshal([]byte(respStr), &response)
	if err != nil {
		return "", stderr.Wrap(err, "failed to unmarshal dify import response")
	}

	if response.AppID == "" {
		return "", stderr.Errorf("dify import API returned empty app_id")
	}
	return response.AppID, nil
}

func (r *Resource) difyPublishApp(ctx context.Context, appID string) error {
	url := DifyAPIBaseURL + fmt.Sprintf(DifyPublishPath, appID)
	requestBody := &models.DifyPublishRequestBody{}
	headers := map[string]string{
		"Authorization": DifyBearerToken,
		"Content-Type":  DifyContentType,
	}
	reqBodyJson, err := json.Marshal(requestBody)
	if err != nil {
		return stderr.Wrap(err, "failed to marshal request body")
	}

	httpParam := &pkgclients.HttpParam{
		Method:  "POST",
		Url:     url,
		ReqBody: string(reqBodyJson),
		Header:  headers,
	}

	var response models.DifyPublishResponseBody
	httpCli := getDifyHttpClient()
	err = httpCli.HttpCall(ctx, httpParam, &response)
	if err != nil {
		return stderr.Wrap(err, "failed to call dify publish API")
	}

	return nil
}

// 调用dify获取站点token API
func (r *Resource) difyGetSiteToken(ctx context.Context, appID string) (string, error) {
	url := DifyAPIBaseURL + fmt.Sprintf(DifySiteTokenPath, appID)
	requestBody := &models.DifySiteTokenRequestBody{}
	headers := map[string]string{
		"Authorization": DifyBearerToken,
		"Content-Type":  DifyContentType,
	}

	reqBodyJson, err := json.Marshal(requestBody)
	if err != nil {
		return "", stderr.Wrap(err, "failed to marshal request body")
	}
	httpParam := &pkgclients.HttpParam{
		Method:  "POST",
		Url:     url,
		ReqBody: string(reqBodyJson),
		Header:  headers,
	}

	var response models.DifySiteTokenResponseBody
	httpCli := getDifyHttpClient()
	err = httpCli.HttpCall(ctx, httpParam, &response)
	if err != nil {
		return "", stderr.Wrap(err, "failed to call dify site token API")
	}

	if response.AccessToken == "" {
		return "", stderr.Errorf("dify site token API returned empty access_token")
	}

	return response.AccessToken, nil
}

func (r *Resource) difyCheckDependencies(ctx context.Context, appID string) error {
	url := DifyAPIBaseURL + fmt.Sprintf(DifyCheckDepsPath, appID)
	headers := map[string]string{
		"Authorization": DifyBearerToken,
		"Content-Type":  DifyContentType,
	}

	httpParam := &pkgclients.HttpParam{
		Method: "GET",
		Url:    url,
		Header: headers,
	}

	var response models.DifyCheckDependenciesResponseBody
	httpCli := getDifyHttpClient()
	err := httpCli.HttpCall(ctx, httpParam, &response)
	if err != nil {
		return stderr.Wrap(err, "failed to call dify check dependencies API")
	}

	if len(response.LeakedDependencies) > 0 {
		depsJson, _ := json.Marshal(response.LeakedDependencies)
		return stderr.Errorf("dify应用导入失败，本平台缺少如下的依赖 %s", string(depsJson))
	}

	return nil
}

// 预处理YAML内容，替换部分插件为我们已有的插件和模型配置
func (r *Resource) preProcessYamlContent(yamlContent string) (string, error) {
	workingContent := r.normalizeYamlContent(yamlContent)

	processedContent, err := r.processYamlWithParser(workingContent)
	if err != nil {
		return "", stderr.Wrap(err, "failed to process yaml with parser")
	}

	return processedContent, nil
}

func (r *Resource) normalizeYamlContent(yamlContent string) string {
	if strings.Contains(yamlContent, "\\n") {
		yamlContent = strings.ReplaceAll(yamlContent, "\\n", "\n")
	}

	yamlContent = strings.ReplaceAll(yamlContent, "\\t", "\t")
	yamlContent = strings.ReplaceAll(yamlContent, "\\r", "\r")

	yamlContent = strings.TrimSpace(yamlContent)

	// 4. 如果内容是被引号包裹的，去除引号
	if (strings.HasPrefix(yamlContent, "\"") && strings.HasSuffix(yamlContent, "\"")) ||
		(strings.HasPrefix(yamlContent, "'") && strings.HasSuffix(yamlContent, "'")) {
		yamlContent = yamlContent[1 : len(yamlContent)-1]
		// 再次处理可能在引号内的转义字符
		yamlContent = strings.ReplaceAll(yamlContent, "\\\"", "\"")
		yamlContent = strings.ReplaceAll(yamlContent, "\\'", "'")
	}

	return yamlContent
}

// 使用YAML解析器处理内容,解析为通用map结构
func (r *Resource) processYamlWithParser(yamlContent string) (string, error) {
	var yamlData map[string]interface{}
	err := yaml.Unmarshal([]byte(yamlContent), &yamlData)
	if err != nil {
		return "", stderr.Wrap(err, "failed to unmarshal yaml")
	}

	err = r.processDependencies(yamlData)
	if err != nil {
		return "", stderr.Wrap(err, "failed to process dependencies")
	}
	err = r.processLLMModels(yamlData)
	if err != nil {
		return "", stderr.Wrap(err, "failed to process LLM models")
	}

	result, err := yaml.Marshal(yamlData)
	if err != nil {
		return "", stderr.Wrap(err, "failed to marshal yaml")
	}

	return string(result), nil
}

// 处理dependencies中的marketplace_plugin_unique_identifier
func (r *Resource) processDependencies(yamlData map[string]interface{}) error {
	dependencies, exists := yamlData["dependencies"]
	if !exists {
		return nil
	}
	depList, ok := dependencies.([]interface{})
	if !ok {
		return nil
	}
	for _, dep := range depList {
		depMap, ok := dep.(map[string]interface{})
		if !ok {
			continue
		}
		value, exists := depMap["value"]
		if !exists {
			continue
		}
		valueMap, ok := value.(map[string]interface{})
		if !ok {
			continue
		}
		pluginId, exists := valueMap["marketplace_plugin_unique_identifier"]
		if !exists {
			continue
		}
		pluginIdStr, ok := pluginId.(string)
		if !ok {
			continue
		}
		// 检查是否包含google关键词，因为谷歌这个插件比较特殊，所以要单独处理一下
		if strings.Contains(pluginIdStr, "langgenius/google") {
			valueMap["marketplace_plugin_unique_identifier"] = "langgenius/google:0.0.9@d360bbc433f39be1b11909cb9c32e6be4a17ea06af083f9e1c7613bb802bf517"
			continue
		}

		// 检查模型相关关键词
		keywords := []string{"volcengine_maas", "deepseek", "wenxin", "modelscope", "openai", "siliconflow"}
		for _, keyword := range keywords {
			if strings.Contains(pluginIdStr, "langgenius/"+keyword) {
				valueMap["marketplace_plugin_unique_identifier"] = ReplacementPluginID
				break
			}
		}
	}

	return nil
}

// 处理LLM节点的模型配置
func (r *Resource) processLLMModels(yamlData map[string]interface{}) error {
	workflow, exists := yamlData["workflow"]
	if !exists {
		return nil
	}
	workflowMap, ok := workflow.(map[string]interface{})
	if !ok {
		return nil
	}
	graph, exists := workflowMap["graph"]
	if !exists {
		return nil
	}
	graphMap, ok := graph.(map[string]interface{})
	if !ok {
		return nil
	}
	nodes, exists := graphMap["nodes"]
	if !exists {
		return nil
	}
	nodesList, ok := nodes.([]interface{})
	if !ok {
		return nil
	}
	for _, node := range nodesList {
		nodeMap, ok := node.(map[string]interface{})
		if !ok {
			continue
		}
		data, exists := nodeMap["data"]
		if !exists {
			continue
		}
		dataMap, ok := data.(map[string]interface{})
		if !ok {
			continue
		}
		nodeType, exists := dataMap["type"]
		if !exists {
			continue
		}
		nodeTypeStr, ok := nodeType.(string)
		if !ok || nodeTypeStr != "llm" {
			continue
		}

		// 在llm节点的model中修改name和provider
		model, exists := dataMap["model"]
		if !exists {
			continue
		}
		modelMap, ok := model.(map[string]interface{})
		if !ok {
			continue
		}
		modelMap["name"] = ReplacementModelName
		modelMap["provider"] = ReplacementProvider
	}

	return nil
}
