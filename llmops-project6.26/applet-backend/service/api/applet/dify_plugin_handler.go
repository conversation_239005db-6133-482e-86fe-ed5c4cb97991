package applet

import (
	"context"
	"io"
	"strings"

	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/core/api_tool/dify_plugins"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
)

// UploadDifyPlugin 上传Dify插件
func (r *Resource) UploadDifyPlugin(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	// 获取上传的文件
	file, header, err := request.Request.FormFile("file")
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("文件上传失败: %v", err))
		return
	}
	defer file.Close()

	// 验证文件格式
	if !strings.HasSuffix(header.Filename, ".difypkg") {
		helper.ErrorResponse(response, stderr.BadRequest.Error("文件格式不正确，必须是.difypkg文件"))
		return
	}

	// 读取文件内容
	fileContent, err := io.ReadAll(file)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error("读取文件内容失败: %v", err))
		return
	}

	// 检查全局管理器是否已初始化
	if dify_plugins.GlobalManager == nil {
		helper.ErrorResponse(response, stderr.Internal.Error("Dify插件管理器未初始化"))
		return
	}

	// 调用插件管理器上传并安装
	result, err := dify_plugins.GlobalManager.UploadAndInstallPlugin(ctx, fileContent, header.Filename)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error("安装插件失败: %v", err))
		return
	}

	if !result.Success {
		helper.ErrorResponse(response, stderr.BadRequest.Error("安装插件失败: %s", result.Message))
		return
	}

	helper.SuccessResponse(response, result)
}

// GetInstalledDifyPlugins 获取已安装的Dify插件
func (r *Resource) GetInstalledDifyPlugins(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	// 检查全局管理器是否已初始化
	if dify_plugins.GlobalManager == nil {
		helper.ErrorResponse(response, stderr.Internal.Error("Dify插件管理器未初始化"))
		return
	}

	// 获取插件列表
	result, err := dify_plugins.GlobalManager.GetAllInstalledPluginsWithAuthStatus(ctx)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error("获取插件列表失败: %v", err))
		return
	}

	if !result.Success {
		helper.ErrorResponse(response, stderr.Internal.Error("获取插件列表失败: %s", result.Message))
		return
	}

	helper.SuccessResponse(response, result.Data)
}

// DifyPluginResponse 用于API响应的插件信息结构
type DifyPluginResponse struct {
	ProviderName      string                   `json:"provider_name"`
	PluginUniqueID    string                   `json:"plugin_unique_identifier"`
	PluginName        string                   `json:"plugin_name"`
	PluginDescription string                   `json:"plugin_description"`
	PluginVersion     string                   `json:"plugin_version"`
	AuthStatus        string                   `json:"auth_status"`
	AuthFields        []dify_plugins.AuthField `json:"auth_fields,omitempty"`
}

// convertPluginInfoToResponse 将PluginInfo转换为API响应格式
func convertPluginInfoToResponse(plugins []*dify_plugins.PluginInfo) []DifyPluginResponse {
	responses := make([]DifyPluginResponse, 0, len(plugins))

	for _, plugin := range plugins {
		// 转换授权状态
		authStatus := "no_auth_required"
		switch plugin.AuthStatus {
		case dify_plugins.AuthStatusRequired:
			authStatus = "unauthorized"
		case dify_plugins.AuthStatusAuthorized:
			authStatus = "authorized"
		case dify_plugins.AuthStatusInvalid:
			authStatus = "invalid"
		}

		response := DifyPluginResponse{
			ProviderName:      plugin.PluginID,
			PluginUniqueID:    plugin.UniqueIdentifier,
			PluginName:        plugin.Name,
			PluginDescription: plugin.Description,
			PluginVersion:     plugin.Version,
			AuthStatus:        authStatus,
		}

		// 如果需要授权，可以在这里添加授权字段信息
		// 目前简化处理，不返回具体的授权字段
		if plugin.AuthStatus != dify_plugins.AuthStatusNotRequired {
			response.AuthFields = []dify_plugins.AuthField{}
		}

		responses = append(responses, response)
	}

	return responses
}

// InitDifyPluginManager 初始化Dify插件管理器
func InitDifyPluginManager(ctx context.Context) error {
	// 创建默认配置
	config := dify_plugins.NewDifyApiConfig()

	// 这里可以从配置文件或环境变量中读取配置
	// 暂时使用默认配置

	// 验证配置
	if err := config.Validate(); err != nil {
		return stderr.Internal.Error("Dify配置验证失败: %v", err)
	}

	// 初始化全局管理器
	dify_plugins.InitGlobalManager(config)

	return nil
}
