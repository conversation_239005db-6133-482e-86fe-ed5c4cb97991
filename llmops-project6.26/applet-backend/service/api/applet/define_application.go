package applet

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strings"

	"github.com/emicklei/go-restful/v3"
	"github.com/google/uuid"
	"gorm.io/gorm"
	"transwarp.io/aip/llmops-common/pb"
	spk "transwarp.io/aip/llmops-common/pkg/serving"
	"transwarp.io/applied-ai/aiot/vision-std/clients/cas"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/core/applet"
	"transwarp.io/applied-ai/applet-backend/core/knowledge_base"
	"transwarp.io/applied-ai/applet-backend/core/super_agent"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
)

const (
	PathParameterID              string = "id"
	QueryParameterContain        string = "contain"
	QueryParameterNeedHealthInfo string = "needHealthInfo"
	QueryParameterValueFalse     string = "false"
	QueryParameterValueTrue      string = "true"
	QueryParameterOnlyRunning    string = "only_running"
	// ServiceType 知识库相关服务类型
	ServiceType                 string      = "service-type"
	ServiceTypeChunking         serviceType = "chunking"          //自定义切片
	ServiceTypeRecall           serviceType = "recall"            //自定义召回
	ServiceTypeQuestionClassify serviceType = "question-classify" //自定义问题分类
)

type responseType string

type serviceType string

// ListApplications 应用开发列表
func (r *Resource) ListApplications(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	apps, err := listApplications(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, apps)
}
func (r *Resource) CreateApplication(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			helper.ErrorResponse(response, err)
		}
	}()

	ctx := helper.GenNewCtx(request)
	application := new(models.AppletChainDO)
	if err = request.ReadEntity(application); err != nil {
		err = stderr.Wrap(err, "invalid request")
		return
	}

	user, err := helper.GetUser(ctx)
	if err != nil {
		err = stderr.Wrap(err, "can not find user in context")
		return
	}

	application.Base.Creator = user
	application.Base.ProjectID = helper.GetProjectID(ctx)
	id, err := applet.ChainManager.CreateChain(ctx, application)
	if err != nil {
		err = stderr.Wrap(err, "create applet chain")
		return
	}
	helper.SuccessResponse(response, helper.ID{ID: id})
}
func (r *Resource) UpdateApplication(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			helper.ErrorResponse(response, err)
		}
	}()

	ctx := helper.GenNewCtx(request)
	application := new(models.AppletChainDO)
	if err = request.ReadEntity(application); err != nil {
		err = stderr.Wrap(err, "invalid request")
		return
	}

	if helper.IsEmpty(application.Base.ID) {
		err = stderr.Errorf("chain id is empty str")
		return
	}

	err = applet.ChainManager.UpdateAppletChain(ctx, application.Base.ID, application)
	if err != nil {
		err = stderr.Wrap(err, "update applet chain")
		return
	}

	helper.SuccessResponse(response, helper.ID{ID: application.Base.ID})
}

func (r *Resource) PublishApplication(request *restful.Request, response *restful.Response) {
	r.optApplication(request, response, true)
}

func (r *Resource) CancelApplication(request *restful.Request, response *restful.Response) {
	r.optApplication(request, response, false)
}

func (r *Resource) PublishApplicationCallBack(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	deployReq := new(models.DoDeployReq)
	if err := request.ReadEntity(deployReq); err != nil {
		err = stderr.InvalidParam.Cause(err, "invalid request")
		helper.ErrorResponse(response, err)
		return
	}
	if err := applet.ChainDeployManager.DeployCasCallBack(ctx, deployReq, getCallBackResult(request)); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) optApplication(request *restful.Request, response *restful.Response, expPublish bool) {
	var err error
	optName, opt := "CancelPublish", applet.ChainDeployManager.Offline
	if expPublish {
		optName, opt = "Publish", applet.ChainDeployManager.Deploy
	}
	defer func() {
		if err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, optName+"Application"))
		}
	}()

	req := &models.PublishApplicationReq{}
	ctx := helper.GenNewCtx(request)
	if err = request.ReadEntity(&req); err != nil {
		err = stderr.InvalidParam.Cause(err, "invalid request")
		return
	}
	id := req.ID
	if id == "" {
		err = stderr.InvalidParam.Errorf("id is necessary")
		return
	}
	// 尝试作为外部注册应用处理
	ea, err := r.ea.GetExternalAPP(req.ID)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			// 数据库操作报错
			err = stderr.Wrap(err, "try get %s as external app", req.ID)
			return
		}
		// not external app, do nothing
		err = nil
	} else {
		// 发布外部注册应用(伪)
		if ea.Published == expPublish {
			// 发布状态已符合预期
			stdlog.Infof("the publish status(published=%v) of external app %s has met expectations, do nothing", expPublish, id)
			helper.SuccessResponse(response, helper.EmptyRsp{})
			return
		}
		ea.Published = expPublish
		if err = r.ea.UpdateExternalAPP(ctx, ea); err != nil {
			err = stderr.Wrap(err, "update the publish status of external app")
			return
		}
		stdlog.Infof("%s external app %s successfully", optName, id)
		helper.SuccessResponse(response, helper.EmptyRsp{})
		return
	}
	casInfo := getApprovalInfo(request, req)
	_, err = applet.ChainManager.GetChainByID(ctx, req.ID)
	if err != nil {
		err = stderr.Wrap(err, "%s is neither external app nor applet chain or assistance")
		return
	}
	chainDO, err := applet.ChainManager.GetChainByID(ctx, id)
	if err != nil {
		return
	}
	_, err = opt(ctx, req.ID, casInfo, chainDO.Base.ExperimentInfo.PermissionCfg)
	if err != nil {
		err = stderr.Wrap(err, optName+" applet")
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

// ListAppService 查询运行中的服务,并匹配其对应的体验信息
func (r *Resource) ListAppService(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, "failed to call ListAppService"))
		}
	}()
	ctx := helper.GenNewCtx(request)
	contain := request.QueryParameter(QueryParameterContain)
	onlyRunning := request.QueryParameter(QueryParameterOnlyRunning) == QueryParameterValueTrue
	appExperiServices, err := listSimpleAppExperis(ctx, listAppsReq{
		contain:            contain,
		onlyHealth:         onlyRunning,
		needChainDetail:    false,
		filterEnableAccess: false,
		withExternal:       true,
	})
	if err != nil {
		return
	}
	stdsrv.SuccessResponseMixWithProto(response, appExperiServices)
}

func (r *Resource) ListServiceHealthInfo(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, "failed to call ListServiceHealthInfo"))
		}
	}()
	ctx := helper.GenNewCtx(request)
	chainStatusMap, err := applet.ChainDeployManager.ListCachedTotalState(ctx)
	if err != nil {
		return
	}
	helper.SuccessResponse(response, chainStatusMap)
}

// ListKbAppService 查询知识库相关服务
func (r *Resource) ListKbAppService(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, "failed to call ListKbAppService"))
		}
	}()
	ctx := helper.GenNewCtx(request)
	svcType := serviceType(request.QueryParameter(ServiceType))
	if svcType != ServiceTypeChunking && svcType != ServiceTypeRecall {
		err = stderr.Errorf(fmt.Sprintf("the %s must be %s or %s ", ServiceType, ServiceTypeChunking, ServiceTypeRecall))
		return
	}
	selectStr := request.QueryParameter(helper.QueryParamMlopsStateSelector)
	appExperiServices, err := listSimpleAppExperis(ctx, listAppsReq{
		contain:            "",
		onlyHealth:         false,
		filterEnableAccess: false,
		withExternal:       false,
		svcTypeSelector:    svcType,
		mlopsSelector:      models.GetMlopsSelector(selectStr),
	})
	if err != nil {
		err = stderr.Wrap(err, "list simple app experis ")
		return
	}
	stdlog.Debugf("totally found %d kb services in %s", len(appExperiServices), helper.GetProjectID(ctx))
	helper.SuccessResponse(response, appExperiServices)
}

func (r *Resource) ListAppServiceAsTool(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, "failed to call ListAppServiceAsTool"))
		}
	}()
	ctx := helper.GenNewCtx(request)
	appExperiServices, err := listSimpleAppExperis(ctx, listAppsReq{
		contain:            "",
		onlyHealth:         false,
		needChainDetail:    true,
		filterEnableAccess: false,
		svcTypeSelector:    serviceType(request.QueryParameter(ServiceType)),
	})
	if err != nil {
		return
	}
	serviceTools := make([]*agent_definition.AppletServiceToolDescriber, 0)
	for _, s := range appExperiServices {
		tool, err := cvtAppExperiService2Tool(ctx, s)
		if err != nil {
			return
		}
		tool.DataSourceId = stdsrv.AnyToString(tool)
		serviceTools = append(serviceTools, tool)
	}
	helper.SuccessResponse(response, serviceTools)
}
func (r *Resource) GetAppService(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	id := request.PathParameter(PathParameterID)
	chainDO, err := applet.AppServiceManger.GetAppSvcAsChainDO(ctx, id)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	var chainState *applet.ChainState
	if request.QueryParameter(QueryParameterNeedHealthInfo) == QueryParameterValueTrue {
		chainState, err = applet.ChainDeployManager.GetTotalState(ctx, id)
	} else {
		chainState, err = applet.ChainDeployManager.GetSimpleState(ctx, id)
	}
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	appService := cvtAppChain2AppExperiment(chainDO, chainState)
	AsyncUpdateChainMetrics(id, applet.ChainMetricsTypeVisit)
	helper.SuccessResponse(response, appService)
}
func (r *Resource) ListAppServLabels(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	contain := ""
	appExperiServices, err := listSimpleAppExperis(ctx, listAppsReq{
		contain:            contain,
		onlyHealth:         false,
		needChainDetail:    false,
		filterEnableAccess: false,
	})
	if err != nil {
		helper.ErrorResponse(response, err)
	}
	labels := make([]models.LabelGroups, 0)
	for _, c := range appExperiServices {
		labels = append(labels, c.ExperimentInfo.LabelInfo)
	}
	res := models.MergeLabels(labels)
	helper.SuccessResponse(response, res)
}
func (r *Resource) DebugAssistant(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			helper.ErrorSSEResponseV2(response, stderr.Wrap(err, "failed to call DebugAssistant"))
		}
	}()
	ctx := helper.GenNewCtx(request)
	agentDebugReq := new(models.AssistantDebugReq)
	if err = stdsrv.ReadEntityMixWithProto(request, agentDebugReq); err != nil {
		return
	}

	// 参数校验
	if agentDebugReq.QueryInfo == nil ||
		agentDebugReq.Assistant == nil ||
		agentDebugReq.Assistant.ExperimentInfo == nil {
		err = stderr.Internal.Errorf("invalid req param")
		return
	}
	// 构造调试参数
	chainParam, err := getChainParam(ctx, agentDebugReq)
	if err != nil {
		return
	}
	debugAndResponse(ctx, request, response, chainParam)
}
func (r *Resource) CreateAssistant(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, "failed to call CreateAssistant"))
		}
	}()
	ctx := helper.GenNewCtx(request)
	assistant := new(models.AppletAssistant)
	if err = stdsrv.ReadEntityMixWithProto(request, assistant); err != nil {
		return
	}
	if assistant.ID == "" {
		err = stderr.Error("the id of assistant must be not empty")
		return
	}

	chainDO, err := getCreatedChainDO(ctx, assistant, true)
	if err != nil {
		return
	}
	_, err = applet.ChainManager.CreateChain(ctx, chainDO)
	if err != nil {
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}
func (r *Resource) UpdateAssistant(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, "failed to call UpdateAssistant"))
		}
	}()
	ctx := helper.GenNewCtx(request)
	assistant := new(models.AppletAssistant)
	if err = stdsrv.ReadEntityMixWithProto(request, assistant); err != nil {
		return
	}
	id := request.PathParameter(PathParameterID)
	if assistant.ID != "" && assistant.ID != id {
		err = stderr.Error("the field of id should be empty or be same with path")
		return
	}
	assistant.ID = id

	chainDO, err := getUpdatedChainDO(ctx, assistant, true)
	if err != nil {
		return
	}

	// 将model中的id置为空
	chainDO.Base.ID = ""
	if err = applet.ChainManager.UpdateAppletChain(ctx, assistant.ID, chainDO); err != nil {
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}
func (r *Resource) UpdateAssistantDebugState(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, "failed to call UpdateAssistant"))
		}
	}()
	ctx := helper.GenNewCtx(request)
	chainID := request.PathParameter(PathParameterID)
	_, err = applet.ChainManager.GetChainByID(ctx, chainID)
	if err != nil {
		err = stderr.Wrap(err, "the assistant is not exist, please create it first")
		return
	}

	stateParam := new(models.UpdateDebugStateReq)
	if err = request.ReadEntity(stateParam); err != nil {
		return
	}

	debugStatus, err := models.StateStrTODebugState(stateParam.State)
	if err != nil {
		return
	}
	updateModel := &models.AppletChainDO{
		Base: models.AppletChainBaseDO{
			LastDebugState: debugStatus,
		},
	}
	if err = applet.ChainManager.UpdateAppletChain(ctx, chainID, updateModel); err != nil {
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}
func (r *Resource) GetAssistantChain(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	id := request.PathParameter(PathParameterID)
	chainDO, err := applet.ChainManager.GetChainByID(ctx, id)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	chainBase := chainDO.Base
	if chainBase.CreatedType != models.AppletTypeAssistant {
		err := stderr.Error("the created type of this chain is not applet assistant")
		helper.ErrorResponse(response, err)
		return
	}
	appletAssistant := &models.AppletAssistant{
		ID:             chainBase.ID,
		ExperimentInfo: chainBase.ExperimentInfo,
		LastDebugState: chainBase.LastDebugState.State,
		// 智能体相关信息
		AssistantInfo: *chainBase.AssistantInfo,
		//AgentConfig:        chainBase.AssistantInfo.AgentConfig,
		//LocalFiles:         chainBase.AssistantInfo.LocalFiles,
		//InputGuardrails:    chainBase.AssistantInfo.InputGuardrails,
		//OutputGuardrails:   chainBase.AssistantInfo.OutputGuardrails,
		//ProblemGenInfo:     chainBase.AssistantInfo.ProblemGenInfo,
		//InternetSearchInfo: chainBase.AssistantInfo.InternetSearchInfo,
		PermissionAction: chainBase.PermissionAction,
		PermissionCfg:    chainBase.PermissionCfg,
	}
	AsyncUpdateChainMetrics(id, applet.ChainMetricsTypeVisit)
	stdsrv.SuccessResponseMixWithProto(response, appletAssistant)
}
func (r *Resource) GetExperimentInfo(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, "failed to call GetExperimentInfo"))
		}
	}()
	ctx := helper.GenNewCtx(request)
	id := request.PathParameter(PathParameterID)
	chainDO, err := applet.ChainManager.GetChainByID(ctx, id)
	if err != nil {
		return
	}
	helper.SuccessResponse(response, chainDO.Base.ExperimentInfo)
}
func (r *Resource) UpdateChainExperiment(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, "failed to call UpdateExperimentInfo"))
		}
	}()
	ctx := helper.GenNewCtx(request)
	id := request.PathParameter(PathParameterID)
	experimentInfo := new(models.ExperimentInfo)
	if err = request.ReadEntity(&experimentInfo); err != nil {
		return
	}
	appletChainDO := &models.AppletChainDO{
		Base: models.AppletChainBaseDO{
			Name:           experimentInfo.Name,
			Labels:         experimentInfo.LabelInfo,
			ExperimentInfo: experimentInfo,
		},
	}
	err = applet.ChainManager.UpdateAppletChain(ctx, id, appletChainDO)
	if err != nil {
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) GetAssistantNodesInfo(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	agentDebugReq := new(models.AssistantDebugReq)
	if err := stdsrv.ReadEntityMixWithProto(request, agentDebugReq); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	chainDetail, err := getChainDetail(ctx, agentDebugReq.Assistant, false)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, chainDetail)
}

func (r *Resource) GetRelatedApp(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	ctx = helper.SetProjectID(ctx, "")
	kbId := request.PathParameter(PathParameterID)
	dependencyInfos, err := applet.ChainManager.GetAppDependencyInfos(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	resp := make([]*models.SimpleChainInfo, 0)
	for _, dp := range dependencyInfos {
		_, ok := dp.KnowledgeBaseIds[kbId]
		if ok {
			resp = append(resp, dp.SimpleChainInfo)
		}
	}
	helper.SuccessResponse(response, resp)
}

func (r *Resource) CountVisitInfo(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	id := request.PathParameter(PathParameterID)
	updateReq := new(models.UpdateVisitInfoReq)
	if err := request.ReadEntity(updateReq); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	if updateReq.ChainId != id {
		helper.ErrorResponse(response, stderr.Error("the id must be same with path param"))
		return
	}
	mType := applet.ChainMetricsType(updateReq.ChainMetricsType)
	if err := applet.ChainMetricsManager.UpdateOnce(ctx, updateReq.ChainId, mType); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) AIGenerateChain(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	req := new(models.AIGenerateChainReq)
	if err := request.ReadEntity(req); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	chain, err := super_agent.SuperAgentImpl.GenerateChain(ctx, req.Query, req.ChatHistory)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, &models.ChainInfo{
		ChainDetail: chain,
	})
}

func getUpdatedChainDO(ctx context.Context, assistant *models.AppletAssistant, needUpdate bool) (*models.AppletChainDO, error) {
	chainDO, err := getCreatedChainDO(ctx, assistant, needUpdate)
	if err != nil {
		return nil, err
	}
	chainDO.Base.Creator = ""
	chainDO.Base.ProjectID = ""
	return chainDO, nil
}
func getCreatedChainDO(ctx context.Context, assistant *models.AppletAssistant, needUpdate bool) (*models.AppletChainDO, error) {
	chainDetail, err := getChainDetail(ctx, assistant, needUpdate)
	if err != nil {
		return nil, err
	}
	creator, err := helper.GetUser(ctx)
	if err != nil {
		return nil, err
	}

	var lastDebugState *models.ChainDebugState
	if assistant.LastDebugState == "" {
		lastDebugState = nil
	} else {
		lastDebugState, err = models.StateStrTODebugState(assistant.LastDebugState)
		if err != nil {
			return nil, err
		}
	}

	chainDO := &models.AppletChainDO{
		ChainDetail: chainDetail,
		Base: models.AppletChainBaseDO{
			ID:             assistant.ID,
			Name:           assistant.ExperimentInfo.Name,
			Creator:        creator,
			Labels:         assistant.ExperimentInfo.LabelInfo,
			ProjectID:      helper.GetProjectID(ctx),
			CreatedType:    models.AppletTypeAssistant,
			ExperimentInfo: assistant.ExperimentInfo,
			LastDebugState: lastDebugState,
			UsageType:      models.ChainUsageTypeLLM,
			AssistantInfo: &models.AssistantInfo{
				AgentConfig:        assistant.AgentConfig,
				LocalFiles:         assistant.LocalFiles,
				InputGuardrails:    assistant.InputGuardrails,
				OutputGuardrails:   assistant.OutputGuardrails,
				ProblemGenInfo:     assistant.ProblemGenInfo,
				InternetSearchInfo: assistant.InternetSearchInfo,
			},
			PermissionAction: assistant.PermissionAction,
			PermissionCfg:    assistant.PermissionCfg,
		},
	}
	return chainDO, nil
}

// 通过智能体信息填充模板,构造应用链
func getChainDetail(ctx context.Context, assistant *models.AppletAssistant, needUpdate bool) (*widgets.Chain, error) {
	chainTemplate, _, err := applet.ChainTemplateManger.GetAssistantTemplate(ctx)
	if err != nil {
		return nil, err
	}

	chainDetail, err := helper.DeepCopy(*chainTemplate.Template)
	if err != nil {
		return nil, err
	}

	safetyConfig := &widgets.SafetyConfig{
		ID:               assistant.ID,
		ProjectID:        assistant.ID,
		InputGuardrails:  &assistant.InputGuardrails,
		OutputGuardrails: &assistant.OutputGuardrails,
	}
	if needUpdate {
		//// 1、更新对应的安全护栏信息,统一在生成tick时处理
		//if err = guardrails.GuardrailsManager.UpsertSafetyConfig(ctx, safetyConfig, true); err != nil {
		//	return nil, err
		//}

		// 2、更新agent相关的知识库信息
		err = updateAgentKbStatus(ctx, assistant)
		if err != nil {
			return nil, err
		}
	}

	// 3.1 将知识库工具以及本地文件统一为知识库工具
	kbs, err := helper.DeepCopy(assistant.AgentConfig.KnowledgeBases)
	if err != nil {
		return nil, err
	}
	if len(assistant.LocalFiles) != 0 {
		kbs.KnowledgeBaseDesc = append(kbs.KnowledgeBaseDesc, cvtLocalFiles2KbTool(assistant.ID, assistant.ExperimentInfo.Name, assistant.LocalFiles))
	}

	//3.2 检查rerank模型是否配置
	if (len(kbs.KnowledgeBaseDesc) > 1) && !existRerankModel(kbs.RerankParams) {
		return nil, stderr.Errorf("please set rerank model to handler result from mutil knowledge base")
	}

	if (len(kbs.StandardKnowledgeBaseDesc) > 1) && !existRerankModel(kbs.RerankParams) {
		return nil, stderr.Errorf("please set rerank model to handler result from mutil standard knowledge base")
	}

	// 3.3 将rerank参数设置到各个知识库工具
	agent_definition.SetKbsRerankParams(&kbs.KnowledgeBases)

	// 4 填充应用链模板
	for _, chainNode := range chainDetail.Nodes {
		widgetKey := chainNode.WidgetId
		switch widgetKey {
		case widgets.WidgetKeyAgent:
			agentConfig := assistant.AgentConfig
			chainNode.Values[widgets.ParamIDPrompt] = agentConfig.Prompt
			chainNode.Values[widgets.ParamIDLLMModelSvcStr] = helper.MixedToString(agentConfig.LLMModelSvc)
			chainNode.Values[widgets.ParamIDApiCollectionsStr] = helper.MixedToString(agentConfig.APICollections)
			chainNode.Values[widgets.ParamIDSystemServicesStr] = helper.MixedToString(agentConfig.SystemServices)
			if !kbs.MustUse {
				chainNode.Values[widgets.ParamIDKnowledgeBaseDescStr] = helper.MixedToString(kbs.KnowledgeBaseDesc)
			} else {
				chainNode.Values[widgets.ParamIDKnowledgeBaseDescStr] = ""
			}

			// 配置溯源模型
			if kbs.RerankParams != nil && kbs.RerankParams.Model != nil {
				agentConfig.RerankModelSvc = kbs.RerankParams.Model
			} else {
				var tempErr error
				agentConfig.RerankModelSvc, tempErr = knowledge_base.DefaultRerankModelSvc(helper.GetProjectID(ctx))
				if tempErr != nil {
					agentConfig.RerankModelSvc = nil
					stdlog.Infof("the default rerank for current project is not set")
				}
			}
			chainNode.Values[widgets.ParamIDEnableTrace] = agentConfig.RerankModelSvc == nil
			chainNode.Values[widgets.ParamIDRerankModelSvcStr] = helper.MixedToString(agentConfig.RerankModelSvc)

		case widgets.WidgetKeyTextKnowledgeSearch:
			var model *pb.ModelService
			topK := conf.Config.AgentConfig.KBConfig.RerankTopK
			threshold := conf.Config.AgentConfig.KBConfig.Threshold
			if kbs.RerankParams != nil {
				model = kbs.RerankParams.Model
				topK = kbs.RerankParams.TopK
				threshold = kbs.RerankParams.ScoreThreshold
			}
			chainNode.Values[widgets.ParamIDRerankTopK] = float64(topK)
			chainNode.Values[widgets.ParamIDRerankThreshold] = float64(threshold)
			chainNode.Values[widgets.ParamIDRerankModel] = helper.MixedToString(model)
			chainNode.Values[widgets.ParamIDEnableMutil] = true
			if kbs.MustUse {
				chainNode.Values[widgets.ParamIDKnowledgeBaseDesc] = helper.MixedToString(kbs.KnowledgeBaseDesc)
			} else {
				chainNode.Values[widgets.ParamIDKnowledgeBaseDesc] = ""
			}

		case widgets.WidgetKeyQaSearch:
			chainNode.Values[widgets.ParamIDQaSearchScoreThreshold] = float64(kbs.StandardScoreThreshold)
			chainNode.Values[widgets.ParamIDQaSearchKnowledge] = helper.MixedToString(kbs.StandardKnowledgeBaseDesc)
			if existRerankModel(kbs.RerankParams) {
				chainNode.Values[widgets.ParamIDQaSearchRerankModel] = helper.MixedToString(kbs.RerankParams.Model)
			} else {
				chainNode.Values[widgets.ParamIDQaSearchRerankModel] = ""
			}

		case widgets.WidgetKeyInternetSearch:
			chainNode.Values[widgets.ParamIDInternetSearchEngine] = string(script.HTTPCallServiceTypeBingSearch)
			chainNode.Values[widgets.ParamIDInternetSearchParseUrl] = assistant.InternetSearchInfo.ParseUrl
			chainNode.Values[widgets.ParamIDInternetSearchEnable] = assistant.InternetSearchInfo.Enable

		case widgets.WidgetKeyInputGuardrail:
			chainNode.Values[widgets.InputGuardrailParamIDStrategy] = helper.MixedToString(safetyConfig.InputGuardrails)
			chainNode.Values[widgets.InputGuardrailParamIDStrategyID] = safetyConfig.ID

		case widgets.WidgetKeyOutputGuardrail:
			chainNode.Values[widgets.OutputGuardrailParamIDStrategy] = helper.MixedToString(safetyConfig.OutputGuardrails)
			chainNode.Values[widgets.OutputGuardrailParamIDStrategyID] = safetyConfig.ID
		}

	}
	return &chainDetail, nil
}
func getChainParam(ctx context.Context, assistantDebugReq *models.AssistantDebugReq) (*models.ChainParam, error) {
	_, chainParamDemo, err := applet.ChainTemplateManger.GetAssistantTemplate(ctx)
	chainParam, err := helper.DeepCopy(*chainParamDemo)
	if err != nil {
		return nil, err
	}
	queryInfo := assistantDebugReq.QueryInfo
	for _, nodeParams := range chainParam.Params {
		for key, _ := range nodeParams.Params {
			if key == widgets.ParamIDTextInput {
				nodeParams.Params[key].Value = queryInfo.Query
			}
			if key == widgets.ParamIDFileInput {
				value := make([]map[string]string, 0)
				value = append(value, map[string]string{"url": queryInfo.File})
				nodeParams.Params[key].Value = value
			}
			if key == widgets.ParamIDChatInput {
				nodeParams.Params[key].Value = queryInfo.History
			}
		}
	}

	assistantInfo := assistantDebugReq.Assistant
	uuid := uuid.New().String()
	chainParam.ChainID = uuid
	chainParam.ChainName = uuid
	if assistantInfo.ID != "" {
		chainParam.ChainID = assistantInfo.ID
	}
	if assistantInfo.ExperimentInfo.Name == "" {
		chainParam.ChainName = assistantInfo.ExperimentInfo.Name
	}

	chainDetail, err := getChainDetail(ctx, assistantInfo, true)
	if err != nil {
		return nil, err
	}
	chainParam.ChainDetail = chainDetail

	chainParam.DebugName = assistantDebugReq.QueryInfo.Query

	return &chainParam, nil
}
func cvtChainState2AppServiceInfo(chainState *applet.ChainState) *models.AppServiceInfo {
	appServiceInfo := new(models.AppServiceInfo)
	appServiceInfo.ID = chainState.ServiceID
	appServiceInfo.Status = chainState.ChainOnlineState
	appServiceInfo.StateIno = chainState.ChainStateInfo
	appServiceInfo.HealthInfo = chainState.ChainHealthyState
	return appServiceInfo
}
func updateAgentKbStatus(ctx context.Context, assistant *models.AppletAssistant) error {
	if len(assistant.LocalFiles) != 0 {
		pbUserCtx, err := helper.GetUserContext(ctx)
		if err != nil {
			return err
		}
		kbm := knowledge_base.GetKnowledgeBaseManager()
		name := conf.Config.AgentConfig.KBConfig.NamePrefix + assistant.ExperimentInfo.Name

		kbRsp, err := kbm.GetKnowledgeBase(ctx, &pb.GetKnowledgeBaseReq{UserContext: pbUserCtx, Id: assistant.ID})
		if err != nil {
			err = stderr.Wrap(err, "failed to get knowledge base")
			return err
		}
		kb := kbRsp.Result.KnowledgeBase
		kb.IsRetrievable = true
		kb.IsVisible = true
		kb.Name = name
		req := &pb.UpdateKnowledgeBaseReq{
			UserContext: pbUserCtx,
			Base:        kb,
		}
		_, err = kbm.UpdateKnowledgeBase(ctx, req)
		if err != nil {
			err = stderr.Wrap(err, "failed to update knowledge base")
			return err
		}
	}
	return nil
}

type listAppsReq struct {
	contain            string              // 根据应用的名称+描述(开场白)进行关键词过滤
	onlyHealth         bool                // 是否仅返回为健康状态的应用
	needChainDetail    bool                // 是否一同返回服务对应的应用链详情
	filterEnableAccess bool                // 是否根据用户对应用的可见性进行过滤
	withExternal       bool                // 是否返回外部自定义应用列表
	svcTypeSelector    serviceType         // 服务类型过滤,用于筛选知识库切分、问题分类等自定义策略
	mlopsSelector      []spk.MLopsSvcState // mlops服务状态筛选
}

// listSimpleAppExperis 获取应用体验(已发布的应用)的简要信息, 包含健康信息-缓存
func listSimpleAppExperis(ctx context.Context, req listAppsReq) ([]*models.AppExperimentService, error) {
	// 1、查询出所有的应用
	chainDOs, err := applet.AppServiceManger.ListAppSvcAsChainDO(ctx)
	if err != nil {
		return nil, err
	}
	// 查询应用助手&应用链
	apps := make([]*models.AppExperimentService, 0)
	statusMap, err := applet.ChainDeployManager.ListCachedTotalState(ctx)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	for _, chainDO := range chainDOs {
		status, ok := statusMap[chainDO.Base.ID]
		if !ok {
			// 不存在部署信息
			continue
		}
		apps = append(apps, cvtAppChain2AppExperiment(chainDO, status))
	}
	// 查询外部注册应用
	if req.withExternal {
		exps, e := listExternalAppsAsExperis(ctx)
		if e != nil {
			err = stderr.Wrap(e, "list external apps as expris")
			return nil, err
		}
		apps = append(apps, exps...)
	}

	// 仅查询状态为健康的
	apps = doOnlyHealthSelector(ctx, apps, req.onlyHealth)

	// 过滤关键词
	apps = filterAppExperisByKeyWord(ctx, apps, req.contain)

	// 筛选服务类型
	apps = doSvcTypeSelector(ctx, apps, req.svcTypeSelector)

	// 筛选mlops服务状态
	apps = doMlopsSelector(ctx, apps, req.mlopsSelector)

	// 按更新时间排序
	sort.Slice(apps, func(i, j int) bool {
		// 最晚创建的服务排在最前
		return apps[i].CreateTime > apps[j].CreateTime
	})
	return apps, nil
}

// listApplications 查询应用列表  包含应用链应用、智能助手应用、外部应用
func listApplications(ctx context.Context) ([]*models.AppletChainBaseDO, error) {
	projectID := helper.GetProjectID(ctx)
	chainDOs, err := applet.ChainManager.SearchForSimpleChainDO(ctx, &applet.SearchChainParam{
		ProjectID: projectID,
	})
	if err != nil {
		stderr.Wrap(err, "list internal apps")
	}
	chainStatusMap, err := applet.ChainDeployManager.ListSimpleStateAsMap(ctx)
	if err != nil {
		return nil, stderr.Wrap(err, "list internal app states")
	}
	apps := make([]*models.AppletChainBaseDO, 0)
	for _, chainDO := range chainDOs {
		app := &chainDO.Base
		if status, ok := chainStatusMap[app.ID]; !ok {
			app.Published = false
		} else {
			// 存在,代表已发布
			app.Published = true
			app.ServiceInfo = cvtChainState2AppServiceInfo(status)
		}
		apps = append(apps, app)
	}

	eApps, err := listExternalAppsAsApplications(ctx)
	if err != nil {
		err = stderr.Wrap(err, "list external apps")
		return nil, err
	}
	apps = append(apps, eApps...)
	sort.Slice(apps, func(i, j int) bool {
		return apps[i].UpdateTime > apps[j].UpdateTime
	})
	return helper.FilterAndSetPermission(ctx, apps, cas.ObjType_AppletChain)
}

func cvtAppExperiService2Tool(ctx context.Context, appExperiSvc *models.AppExperimentService) (*agent_definition.AppletServiceToolDescriber, error) {
	// params构造
	params := make([]agent_definition.AppletServiceToolParam, 0)
	for _, node := range appExperiSvc.ChainDetail.Nodes {
		if node.WidgetDetail.Group == widgets.WidgetGroupInput {
			param, err := getToolParamByNode(&node)
			if err != nil {
				return nil, err
			}
			// 智能助手只有问题文本必填
			if appExperiSvc.CreatedType == models.AppletTypeAssistant && node.WidgetId != widgets.WidgetKeyTextInput {
				param.Required = false
			}
			params = append(params, *param)
		}
	}

	res := &agent_definition.AppletServiceToolDescriber{
		ID:           appExperiSvc.ID,
		MlOpsSvcID:   appExperiSvc.AppServiceInfo.ID,
		Name:         appExperiSvc.ExperimentInfo.Name,
		Desc:         appExperiSvc.ExperimentInfo.Introduction,
		CreateTimeMs: appExperiSvc.CreateTime,
		Params:       params,
	}

	return res, nil
}

func getToolParamByNode(node *widgets.ChainNode) (*agent_definition.AppletServiceToolParam, error) {
	res := &agent_definition.AppletServiceToolParam{
		Required: true,
	}

	switch node.WidgetId {
	case widgets.WidgetKeyChatHistory:
		res.ValueType = triton.ParameterTypeArray
		res.DefaultValue = []*triton.QAItem{{Q: "用户问题1", A: "模型回答1"}, {Q: "用户问题2", A: "模型回答2"}}
		res.Name = widgets.ParamIDChatInput
		res.Desc = fmt.Sprintf("该参数代表用户的历史问答对,参数名称为:%s, 参数类型为:%s, 具体示例为: %s", res.Name, res.ValueType, stdsrv.AnyToString(res.DefaultValue))

	case widgets.WidgetKeyFileInput:
		res.ValueType = triton.ParameterTypeArray
		res.DefaultValue = []map[string]string{{"url": "sfs:///tenants/dev-assets/projs/assets/test.txt"}}
		res.Name = widgets.ParamIDFileInput
		res.Desc = fmt.Sprintf("该参数代表用户输入的文件信息,参数名称为:%s, 类型为:%s, 其中的url字段表示具体的文件地址,并且只支持传入一个文件"+
			"具体示例为: %s", res.Name, res.ValueType, stdsrv.AnyToString(res.DefaultValue))

	case widgets.WidgetKeyTextInput:
		res.Required = true
		res.ValueType = triton.ParameterTypeString
		res.DefaultValue = "你好，请简单介绍自身作用。"
		res.Name = widgets.ParamIDTextInput
		res.Desc = fmt.Sprintf("该参数代表用户输入的文本信息,参数名称为:%s, 参数类型为:%s, 具体示例为: %s", res.Name, res.ValueType, stdsrv.AnyToString(res.DefaultValue))

	case widgets.WidgetKeyUpstreamInput:
		res.ValueType = triton.ParameterTypeObject
		res.DefaultValue = "{}"
		res.Name = widgets.ParamIDJsonInput
		res.Desc = fmt.Sprintf("该参数代表用户输入的json对象,参数名称为:%s, 参数类型为:%s, 具体示例为: %s", res.Name, res.ValueType, stdsrv.AnyToString(res.DefaultValue))

	default:
		return nil, stderr.Error("the group of node must be WidgetGroupInput")
	}
	return res, nil
}
func cvtAppChain2AppExperiment(chainDO *models.AppletChainDO, status *applet.ChainState) *models.AppExperimentService {
	if chainDO == nil || status == nil {
		return nil
	}

	app := &models.AppExperimentService{
		ID:             status.ChainID,
		ProjectId:      chainDO.Base.ProjectID,
		ExperimentInfo: chainDO.Base.ExperimentInfo,
		AppServiceInfo: &models.AppServiceInfo{
			ID:         status.ServiceID,
			Name:       status.ServiceName,
			VirtualUrl: status.VirtualUrl,
			Status:     status.ChainOnlineState,
			StateIno:   status.ChainStateInfo,
			HealthInfo: status.ChainHealthyState,
		},
		ChainDetail: chainDO.ChainDetail,
		CreatedType: chainDO.Base.CreatedType,
		UsageType:   chainDO.Base.UsageType,
		CreateTime:  chainDO.Base.CreateTime,
	}
	if app.ExperimentInfo != nil {
		app.ExperimentInfo.PermissionCfg = status.PermissionCfg
		app.ExperimentInfo.PermissionAction = status.PermissionAction
	}
	if a := chainDO.Base.AssistantInfo; a != nil {
		app.ProblemGenInfo = a.ProblemGenInfo
	}
	return app
}

//func setAccessible(ctx context.Context, app *models.AppExperimentService) error {
//	token, err := helper.GetToken(ctx)
//	if err != nil {
//		return err
//	}
//
//	// 获取当前登录的用户信息
//	curName, err := helper.GetUser(ctx)
//	if err != nil {
//		return err
//	}
//
//	// 获取用户组信息，用于判断当前用户是否能访问
//	gMap, err := stdsrv.GetGroupsMap(token)
//	if err != nil {
//		return err
//	}
//	app.EnableAccess = stdsrv.BelongTo(curName, app.ExperimentInfo.Members, gMap)
//	return nil
//}

func filterAppExperisByKeyWord(ctx context.Context, apps []*models.AppExperimentService, keyword string) (ret []*models.AppExperimentService) {
	if keyword == "" {
		return apps
	}
	for _, app := range apps {
		strTemp := app.ExperimentInfo.Name + app.ExperimentInfo.Introduction
		if strings.Contains(strings.ToLower(strTemp), strings.ToLower(keyword)) {
			ret = append(ret, app)
		}
	}
	return ret
}
func doOnlyHealthSelector(ctx context.Context, apps []*models.AppExperimentService, onlyHealth bool) (ret []*models.AppExperimentService) {
	if !onlyHealth {
		return apps
	}
	for _, app := range apps {
		if app.AppServiceInfo == nil ||
			app.AppServiceInfo.HealthInfo == nil ||
			app.AppServiceInfo.HealthInfo.Healthy == false {
			continue
		}
		ret = append(ret, app)
	}
	return ret
}

func doSvcTypeSelector(ctx context.Context, apps []*models.AppExperimentService, selector serviceType) (ret []*models.AppExperimentService) {
	if len(selector) == 0 {
		return apps
	}
	for _, app := range apps {
		isMatch := false
		switch selector {
		case ServiceTypeChunking:
			isMatch = models.AppletChainDO{ChainDetail: app.ChainDetail}.IsChunkingChain()
		case ServiceTypeRecall:
			isMatch = models.AppletChainDO{ChainDetail: app.ChainDetail}.IsRecallChain()
		case ServiceTypeQuestionClassify:
			isMatch = models.AppletChainDO{ChainDetail: app.ChainDetail}.IsQuestionClassifierChain()
		}
		if isMatch {
			ret = append(ret, app)
		}
	}
	return ret
}

func doMlopsSelector(ctx context.Context, apps []*models.AppExperimentService, selector []spk.MLopsSvcState) (ret []*models.AppExperimentService) {
	if len(selector) == 0 {
		return apps
	}
	selectorMap := helper.CvtSlice2Map(selector, func(value spk.MLopsSvcState) string { return string(value) })
	for _, app := range apps {
		if app.AppServiceInfo == nil || app.AppServiceInfo.StateIno == nil {
			continue
		}
		k := app.AppServiceInfo.StateIno.MLopsSvcState
		if _, ok := selectorMap[string(k)]; ok {
			ret = append(ret, app)
		}
	}
	return ret
}

//func filterAppExperisByMembers(ctx context.Context, apps []*models.AppExperimentService, filterEnableAccess bool) (ret []*models.AppExperimentService, err error) {
//	token, err := helper.GetToken(ctx)
//	if err != nil {
//		return nil, err
//	}
//
//	// 获取当前登录的用户信息
//	curName, err := helper.GetUser(ctx)
//	if err != nil {
//		return nil, err
//	}
//
//	// 获取用户组信息，用于判断当前用户是否能访问
//	gMap, err := stdsrv.GetGroupsMap(token)
//	if err != nil {
//		return nil, err
//	}
//	// 为空则表示全部成员都能访问  或curName属于members
//	for _, app := range apps {
//		app.EnableAccess = stdsrv.BelongTo(curName, app.ExperimentInfo.Members, gMap)
//		if filterEnableAccess && !app.EnableAccess {
//			// 不返回无需保留的无权访问的应用
//			continue
//		}
//		ret = append(ret, app)
//	}
//	return
//}

func cvtLocalFiles2KbTool(kbId string, kbName string, localFiles []*models.LocalFile) *agent_definition.KnowlHubDescriber {
	kbTool := &agent_definition.KnowlHubDescriber{ID: kbId, Name: kbName}
	kbTool.Desc = "这是由临时文件构建的文本知识库,可能与用户问题存在较大联系,该知识库内部包含以下文件: "
	for _, file := range localFiles {
		kbTool.Desc = kbTool.Desc + "\n" + file.Name + ","
		kbTool.EnableDocs = append(kbTool.EnableDocs, &pb.Document{
			DocId: file.DocumentID, DocName: file.Name,
			FilePath: file.Path, FileSizeBytes: int32(file.Size), KnowledgeBaseId: kbId,
		})
	}
	return kbTool
}

// 构建用户组信息的map
func cvtUserGroupToMap(groups models.UserGroup) map[string]map[string]struct{} {
	result := make(map[string]map[string]struct{})
	for _, group := range groups {
		userMap := make(map[string]struct{})
		for _, userName := range group.UserNames {
			userMap[userName] = struct{}{}
		}
		result[group.Name] = userMap
	}
	return result
}

func getCallBackResult(request *restful.Request) models.ApprovalState {
	approvalState := models.ApprovalStateApproved
	if request.HeaderParameter("Cas-Callback-Result") == "reject" {
		approvalState = models.ApprovalStateRejected
	}
	return approvalState
}

func getApprovalInfo(request *restful.Request, req *models.PublishApplicationReq) *models.CasApprovalInfo {
	res := &models.CasApprovalInfo{
		EnabledApproval: req.Examination != nil,
	}
	if req.Examination != nil {
		res.Examination = *req.Examination
	}
	return res
}

func existRerankModel(p *pb.RerankParams) bool {
	if p != nil && p.Model != nil {
		return true
	}
	return false
}

// ==================== Dify Plugin相关数据结构 ====================

// PluginUploadResponse 插件上传响应
type PluginUploadResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Message string      `json:"message,omitempty"`
}
