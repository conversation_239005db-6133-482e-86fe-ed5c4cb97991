package dify_plugins

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

// Manager Dify插件管理器
type Manager struct {
	config     *DifyApiConfig
	httpClient *http.Client
	installer  *Installer
	mu         sync.RWMutex
}

// GlobalManager 全局插件管理器实例
var GlobalManager *Manager

// NewManager 创建新的插件管理器
func NewManager(config *DifyApiConfig) *Manager {
	return &Manager{
		config: config,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		installer: NewInstaller(config),
	}
}

// InitGlobalManager 初始化全局管理器
func InitGlobalManager(config *DifyApiConfig) {
	GlobalManager = NewManager(config)
}

// GetAllInstalledPluginsWithAuthStatus 获取所有已安装插件信息及授权状态
func (m *Manager) GetAllInstalledPluginsWithAuthStatus(ctx context.Context) (*PluginListResponse, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	stdlog.Infof("开始获取所有已安装插件信息及授权状态")

	// 第一步：获取插件列表，提取provider_name和plugin_unique_identifier
	pluginInfoList, err := m.getPluginListWithProviderInfo(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取插件列表失败: %v", err)
	}

	if len(pluginInfoList) == 0 {
		stdlog.Warnf("未能提取到任何插件的provider信息，可能是数据结构问题")
		return &PluginListResponse{
			Success: false,
			Message: "未能提取到插件的provider信息，请检查插件数据结构",
			Data:    []*PluginInfo{},
		}, nil
	}

	stdlog.Infof("获取到 %d 个插件的基本信息", len(pluginInfoList))

	// 第二步：并行获取授权状态和授权字段信息
	startTime := time.Now()
	providerNames := make([]string, len(pluginInfoList))
	for i, plugin := range pluginInfoList {
		providerNames[i] = plugin.ProviderName
	}

	// 并行获取授权状态和字段
	authStatusChan := make(chan map[string]*AuthInfo, 1)
	authFieldsChan := make(chan map[string][]AuthField, 1)

	go func() {
		authStatus := m.getPluginsAuthStatus(ctx)
		authStatusChan <- authStatus
	}()

	go func() {
		authFields := m.getAllAuthFieldsParallel(ctx, providerNames)
		authFieldsChan <- authFields
	}()

	// 等待结果
	authStatusMap := <-authStatusChan
	authFieldsMap := <-authFieldsChan

	parallelTime := time.Since(startTime)
	stdlog.Infof("并行获取完成，耗时 %.2f秒，授权状态: %d，授权字段: %d",
		parallelTime.Seconds(), len(authStatusMap), len(authFieldsMap))

	// 第三步：快速合并数据
	resultPlugins := make([]*PluginInfo, 0, len(pluginInfoList))
	for _, pluginInfo := range pluginInfoList {
		providerName := pluginInfo.ProviderName
		pluginUniqueIdentifier := pluginInfo.PluginUniqueIdentifier

		// 查找授权状态和字段
		authInfo := authStatusMap[providerName]
		authFields := authFieldsMap[providerName]

		// 确定授权状态
		var authStatus AuthStatus
		if len(authFields) == 0 {
			authStatus = AuthStatusNotRequired
		} else if authInfo != nil && authInfo.IsTeamAuthorization {
			authStatus = AuthStatusAuthorized
		} else {
			authStatus = AuthStatusRequired
		}

		plugin := &PluginInfo{
			PluginID:         providerName,
			Name:             pluginInfo.PluginName,
			Version:          pluginInfo.PluginVersion,
			Author:           pluginInfo.PluginAuthor,
			Description:      pluginInfo.PluginDescription,
			UniqueIdentifier: pluginUniqueIdentifier,
			Status:           PluginStatusInstalled,
			AuthStatus:       authStatus,
			InstallTime:      nil,
			Permissions:      make(map[string]interface{}),
			Metadata:         make(map[string]interface{}),
		}

		resultPlugins = append(resultPlugins, plugin)
	}

	stdlog.Infof("成功处理 %d 个插件的信息", len(resultPlugins))

	return &PluginListResponse{
		Success: true,
		Message: fmt.Sprintf("成功获取 %d 个插件信息", len(resultPlugins)),
		Data:    resultPlugins,
	}, nil
}

// getPluginListWithProviderInfo 第一步：获取插件列表并提取provider信息
func (m *Manager) getPluginListWithProviderInfo(ctx context.Context) ([]*PluginInfoWithProvider, error) {
	url := fmt.Sprintf("%s/console/api/workspaces/current/plugin/list", m.config.BaseURL)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	for key, value := range m.config.GetHeaders() {
		req.Header.Set(key, value)
	}

	stdlog.Debugf("请求URL: %s", url)

	resp, err := m.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	stdlog.Infof("插件列表API响应状态: %d", resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		stdlog.Errorf("获取插件列表失败: HTTP %d, 响应: %s", resp.StatusCode, string(body))
		// 如果插件列表API失败，尝试从工具提供者API获取
		return m.getPluginsFromToolProviders(ctx)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	var data struct {
		Plugins []DifyPlugin `json:"plugins"`
	}
	if err := json.Unmarshal(body, &data); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	plugins := data.Plugins
	stdlog.Infof("从Dify获取到 %d 个插件", len(plugins))

	// 如果没有插件，尝试从工具提供者API获取
	if len(plugins) == 0 {
		stdlog.Warnf("插件列表为空，尝试从工具提供者API获取")
		return m.getPluginsFromToolProviders(ctx)
	}

	pluginInfoList := make([]*PluginInfoWithProvider, 0, len(plugins))

	for _, plugin := range plugins {
		providerName := m.extractProviderName(plugin)
		if providerName == "" {
			stdlog.Warnf("无法提取插件 %s 的provider信息", plugin.Name)
			continue
		}

		// 获取插件显示名称和描述
		displayName := plugin.Name
		description := ""

		if plugin.Declaration != nil {
			if desc, ok := plugin.Declaration["description"]; ok {
				if descMap, ok := desc.(map[string]interface{}); ok {
					if zhDesc, ok := descMap["zh_Hans"].(string); ok {
						description = zhDesc
					} else if enDesc, ok := descMap["en_US"].(string); ok {
						description = enDesc
					}
				} else if descStr, ok := desc.(string); ok {
					description = descStr
				}
			}

			// 尝试获取更好的显示名称
			if tool, ok := plugin.Declaration["tool"].(map[string]interface{}); ok {
				if identity, ok := tool["identity"].(map[string]interface{}); ok {
					if label, ok := identity["label"].(map[string]interface{}); ok {
						if zhLabel, ok := label["zh_Hans"].(string); ok {
							displayName = zhLabel
						} else if enLabel, ok := label["en_US"].(string); ok {
							displayName = enLabel
						}
					}
				}
			}
		}

		pluginInfo := &PluginInfoWithProvider{
			ProviderName:           providerName,
			PluginUniqueIdentifier: plugin.PluginUniqueIdentifier,
			PluginName:             displayName,
			PluginDescription:      description,
			PluginAuthor:           plugin.Author,
			PluginVersion:          plugin.Version,
			OriginalProvider:       providerName,
		}

		pluginInfoList = append(pluginInfoList, pluginInfo)
		stdlog.Debugf("成功提取插件信息: %s -> %s", providerName, plugin.PluginUniqueIdentifier)
	}

	stdlog.Infof("成功提取 %d 个插件的provider信息", len(pluginInfoList))
	return pluginInfoList, nil
}

// extractProviderName 从插件信息中提取provider名称
func (m *Manager) extractProviderName(plugin DifyPlugin) string {
	// 方法1: 从declaration.plugins.tools中提取
	if plugin.Declaration != nil {
		if pluginsInfo, ok := plugin.Declaration["plugins"].(map[string]interface{}); ok {
			if toolsList, ok := pluginsInfo["tools"].([]interface{}); ok {
				for _, toolPath := range toolsList {
					if toolPathStr, ok := toolPath.(string); ok && strings.Contains(toolPathStr, "/") {
						parts := strings.Split(toolPathStr, "/")
						if len(parts) >= 2 {
							providerName := parts[1]
							if strings.HasSuffix(providerName, ".yaml") {
								providerName = providerName[:len(providerName)-5]
							}
							return providerName
						}
					}
				}
			}
		}

		// 方法2: 从declaration.tool.identity.name中提取
		if tool, ok := plugin.Declaration["tool"].(map[string]interface{}); ok {
			if identity, ok := tool["identity"].(map[string]interface{}); ok {
				if name, ok := identity["name"].(string); ok && name != "" {
					return name
				}
			}
		}
	}

	// 方法3: 从plugin_id中提取
	if plugin.PluginID != "" && strings.Contains(plugin.PluginID, "/") {
		parts := strings.Split(plugin.PluginID, "/")
		if len(parts) >= 2 {
			return parts[len(parts)-1] // 取最后一部分
		}
	}

	// 方法4: 使用插件名称作为fallback
	return plugin.Name
}

// getPluginsAuthStatus 第二步：获取插件授权状态
func (m *Manager) getPluginsAuthStatus(ctx context.Context) map[string]*AuthInfo {
	url := fmt.Sprintf("%s/console/api/workspaces/current/tool-providers", m.config.BaseURL)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		stdlog.Errorf("创建授权状态请求失败: %v", err)
		return make(map[string]*AuthInfo)
	}

	for key, value := range m.config.GetHeaders() {
		req.Header.Set(key, value)
	}

	resp, err := m.httpClient.Do(req)
	if err != nil {
		stdlog.Errorf("获取工具提供者列表失败: %v", err)
		return make(map[string]*AuthInfo)
	}
	defer resp.Body.Close()

	stdlog.Debugf("工具提供者API响应状态: %d", resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		stdlog.Errorf("获取工具提供者列表失败: HTTP %d", resp.StatusCode)
		return make(map[string]*AuthInfo)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		stdlog.Errorf("读取工具提供者响应失败: %v", err)
		return make(map[string]*AuthInfo)
	}

	var providers []DifyToolProvider
	if err := json.Unmarshal(body, &providers); err != nil {
		stdlog.Errorf("解析工具提供者响应失败: %v", err)
		return make(map[string]*AuthInfo)
	}

	stdlog.Infof("从Dify获取到 %d 个工具提供者", len(providers))

	authStatusMap := make(map[string]*AuthInfo)

	for _, provider := range providers {
		// 处理复合名称（如 langgenius/wecom -> wecom）
		simpleName := provider.Name
		if strings.Contains(provider.Name, "/") {
			parts := strings.Split(provider.Name, "/")
			simpleName = parts[len(parts)-1]
		}

		authInfo := &AuthInfo{
			IsTeamAuthorization: provider.IsTeamAuthorization,
			TeamCredentials:     provider.TeamCredentials,
			Type:                provider.Type,
			FullName:            provider.Name,
		}

		// 同时用完整名称和简化名称作为key
		authStatusMap[provider.Name] = authInfo
		if simpleName != provider.Name {
			authStatusMap[simpleName] = authInfo
		}

		stdlog.Debugf("工具提供者: %s -> 授权状态: %t", provider.Name, provider.IsTeamAuthorization)
	}

	stdlog.Infof("成功获取 %d 个工具提供者的授权状态", len(authStatusMap))
	return authStatusMap
}

// getAllAuthFieldsParallel 并行获取所有插件的授权字段信息
func (m *Manager) getAllAuthFieldsParallel(ctx context.Context, providerNames []string) map[string][]AuthField {
	authFieldsMap := make(map[string][]AuthField)
	stdlog.Infof("开始并行获取 %d 个插件的授权字段信息", len(providerNames))

	// 使用channel收集结果
	type result struct {
		providerName string
		authFields   []AuthField
	}

	resultChan := make(chan result, len(providerNames))
	semaphore := make(chan struct{}, 10) // 限制并发数为10

	// 启动goroutines
	for _, providerName := range providerNames {
		go func(name string) {
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			authFields := m.getPluginAuthFields(ctx, name)
			resultChan <- result{
				providerName: name,
				authFields:   authFields,
			}
		}(providerName)
	}

	// 收集结果
	for i := 0; i < len(providerNames); i++ {
		res := <-resultChan
		authFieldsMap[res.providerName] = res.authFields
		stdlog.Debugf("✓ %s: %d 个授权字段", res.providerName, len(res.authFields))
	}

	return authFieldsMap
}

// getPluginAuthFields 获取插件授权字段信息
func (m *Manager) getPluginAuthFields(ctx context.Context, providerName string) []AuthField {
	url := fmt.Sprintf("%s/console/api/workspaces/current/tool-provider/builtin/%s/credentials_schema",
		m.config.BaseURL, providerName)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		stdlog.Debugf("创建授权字段请求失败 %s: %v", providerName, err)
		return []AuthField{}
	}

	for key, value := range m.config.GetHeaders() {
		req.Header.Set(key, value)
	}

	resp, err := m.httpClient.Do(req)
	if err != nil {
		stdlog.Debugf("获取 %s 授权字段失败: %v", providerName, err)
		return []AuthField{}
	}
	defer resp.Body.Close()

	stdlog.Debugf("获取 %s 授权字段API响应状态: %d", providerName, resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		stdlog.Debugf("获取 %s 授权字段失败: HTTP %d", providerName, resp.StatusCode)
		return []AuthField{}
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		stdlog.Debugf("读取 %s 授权字段响应失败: %v", providerName, err)
		return []AuthField{}
	}

	var credentialsSchema []DifyCredentialsSchema
	if err := json.Unmarshal(body, &credentialsSchema); err != nil {
		stdlog.Debugf("解析 %s 授权字段响应失败: %v", providerName, err)
		return []AuthField{}
	}

	stdlog.Debugf("获取到 %s 的授权字段: %d 个", providerName, len(credentialsSchema))

	// 提取需要的字段信息
	authFields := make([]AuthField, 0, len(credentialsSchema))
	for _, field := range credentialsSchema {
		// 提取中文标签
		displayLabel := field.Name
		if field.Label != nil {
			if zhLabel, ok := field.Label["zh_Hans"].(string); ok {
				displayLabel = zhLabel
			} else if enLabel, ok := field.Label["en_US"].(string); ok {
				displayLabel = enLabel
			}
		}

		authField := AuthField{
			Name:     field.Name,
			Label:    displayLabel,
			Required: field.Required,
			Type:     field.Type,
		}

		authFields = append(authFields, authField)
		stdlog.Debugf("提取授权字段: %s -> %s (必填: %t)", field.Name, displayLabel, field.Required)
	}

	return authFields
}

// getPluginsFromToolProviders Fallback方法：从工具提供者API获取插件信息
func (m *Manager) getPluginsFromToolProviders(ctx context.Context) ([]*PluginInfoWithProvider, error) {
	stdlog.Infof("使用fallback方法从工具提供者API获取插件信息")

	url := fmt.Sprintf("%s/console/api/workspaces/current/tool-providers", m.config.BaseURL)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	for key, value := range m.config.GetHeaders() {
		req.Header.Set(key, value)
	}

	resp, err := m.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	stdlog.Debugf("工具提供者API响应状态: %d", resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("获取工具提供者列表失败: HTTP %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	var providers []DifyToolProvider
	if err := json.Unmarshal(body, &providers); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	stdlog.Infof("从工具提供者API获取到 %d 个提供者", len(providers))

	pluginInfoList := make([]*PluginInfoWithProvider, 0)

	for _, provider := range providers {
		// 只处理插件类型的提供者
		if provider.Type == "plugin" || provider.Type == "builtin" {
			// 处理复合名称（如 langgenius/wecom -> wecom）
			simpleName := provider.Name
			pluginUniqueIdentifier := provider.Name + ":latest" // 假设版本
			if strings.Contains(provider.Name, "/") {
				parts := strings.Split(provider.Name, "/")
				simpleName = parts[len(parts)-1]
			} else {
				pluginUniqueIdentifier = fmt.Sprintf("builtin/%s:latest", provider.Name)
			}

			// 提取显示名称和描述
			displayName := simpleName
			description := ""

			if provider.Label != nil {
				if zhLabel, ok := provider.Label["zh_Hans"].(string); ok {
					displayName = zhLabel
				} else if enLabel, ok := provider.Label["en_US"].(string); ok {
					displayName = enLabel
				}
			}

			if provider.Description != nil {
				if zhDesc, ok := provider.Description["zh_Hans"].(string); ok {
					description = zhDesc
				} else if enDesc, ok := provider.Description["en_US"].(string); ok {
					description = enDesc
				}
			}

			pluginInfo := &PluginInfoWithProvider{
				ProviderName:           simpleName,
				PluginUniqueIdentifier: pluginUniqueIdentifier,
				PluginName:             displayName,
				PluginDescription:      description,
				PluginAuthor:           provider.Author,
				PluginVersion:          "latest",
				OriginalProvider:       provider.Name,
			}

			pluginInfoList = append(pluginInfoList, pluginInfo)
			stdlog.Debugf("从工具提供者提取插件信息: %s -> %s", simpleName, pluginUniqueIdentifier)
		}
	}

	stdlog.Infof("从工具提供者API成功提取 %d 个插件信息", len(pluginInfoList))
	return pluginInfoList, nil
}

// UploadAndInstallPlugin 上传并安装插件
func (m *Manager) UploadAndInstallPlugin(ctx context.Context, fileContent []byte, filename string) (*PluginUploadResult, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	return m.installer.UploadAndInstall(ctx, fileContent, filename)
}
