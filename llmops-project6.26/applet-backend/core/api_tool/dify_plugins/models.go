package dify_plugins

import (
	"fmt"
	"time"
)

// PluginStatus 插件状态枚举
type PluginStatus string

const (
	PluginStatusUploading   PluginStatus = "uploading"
	PluginStatusInstalling  PluginStatus = "installing"
	PluginStatusInstalled   PluginStatus = "installed"
	PluginStatusFailed      PluginStatus = "failed"
	PluginStatusUninstalled PluginStatus = "uninstalled"
)

// AuthStatus 授权状态枚举
type AuthStatus string

const (
	AuthStatusNotRequired AuthStatus = "not_required"
	AuthStatusRequired    AuthStatus = "required"
	AuthStatusAuthorized  AuthStatus = "authorized"
	AuthStatusInvalid     AuthStatus = "invalid"
)

// DifyApiConfig Dify API配置
type DifyApiConfig struct {
	BaseURL   string `json:"base_url" yaml:"base_url"`
	ApiKey    string `json:"api_key" yaml:"api_key"`
	AuthToken string `json:"auth_token" yaml:"auth_token"`
	TenantID  string `json:"tenant_id" yaml:"tenant_id"`
}

// PluginInfo 插件信息
type PluginInfo struct {
	PluginID          string                 `json:"plugin_id"`
	Name              string                 `json:"name"`
	Version           string                 `json:"version"`
	Author            string                 `json:"author"`
	Description       string                 `json:"description"`
	UniqueIdentifier  string                 `json:"unique_identifier"`
	Status            PluginStatus           `json:"status"`
	AuthStatus        AuthStatus             `json:"auth_status"`
	InstallTime       *time.Time             `json:"install_time,omitempty"`
	Permissions       map[string]interface{} `json:"permissions"`
	Metadata          map[string]interface{} `json:"metadata"`
}

// PluginUploadResult 插件上传结果
type PluginUploadResult struct {
	Success    bool        `json:"success"`
	Message    string      `json:"message"`
	PluginInfo *PluginInfo `json:"plugin_info,omitempty"`
}

// PluginListResponse 插件列表响应
type PluginListResponse struct {
	Success bool          `json:"success"`
	Message string        `json:"message"`
	Data    []*PluginInfo `json:"data"`
}

// DifyPluginListItem Dify API返回的插件列表项
type DifyPluginListItem struct {
	ProviderName         string      `json:"provider_name"`
	PluginUniqueID       string      `json:"plugin_unique_identifier"`
	PluginName           string      `json:"plugin_name"`
	PluginDescription    string      `json:"plugin_description"`
	PluginVersion        string      `json:"plugin_version"`
	AuthStatus           string      `json:"auth_status"`
	AuthFields           []AuthField `json:"auth_fields,omitempty"`
}

// AuthField 授权字段
type AuthField struct {
	Name        string      `json:"name"`
	Type        string      `json:"type"`
	Required    bool        `json:"required"`
	Label       string      `json:"label"`
	Placeholder string      `json:"placeholder,omitempty"`
	Default     interface{} `json:"default,omitempty"`
	Options     []Option    `json:"options,omitempty"`
}

// Option 选项
type Option struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

// DifyPluginListResponse Dify插件列表API响应
type DifyPluginListResponse struct {
	Plugins []DifyPlugin `json:"plugins"`
}

// DifyPlugin Dify插件信息
type DifyPlugin struct {
	PluginUniqueIdentifier string                 `json:"plugin_unique_identifier"`
	PluginName             string                 `json:"plugin_name"`
	PluginDescription      string                 `json:"plugin_description"`
	PluginVersion          string                 `json:"plugin_version"`
	Author                 string                 `json:"author"`
	Metadata               map[string]interface{} `json:"metadata"`
	Declaration            map[string]interface{} `json:"declaration"`
	PluginID               string                 `json:"plugin_id"`
	Name                   string                 `json:"name"`
	Version                string                 `json:"version"`
}

// DifyToolProvider Dify工具提供者
type DifyToolProvider struct {
	Name               string                 `json:"name"`
	Type               string                 `json:"type"`
	IsTeamAuthorization bool                  `json:"is_team_authorization"`
	TeamCredentials    map[string]interface{} `json:"team_credentials"`
	Label              map[string]interface{} `json:"label"`
	Description        map[string]interface{} `json:"description"`
	Author             string                 `json:"author"`
}

// DifyCredentialsSchema 授权字段schema
type DifyCredentialsSchema struct {
	Name     string                 `json:"name"`
	Label    map[string]interface{} `json:"label"`
	Required bool                   `json:"required"`
	Type     string                 `json:"type"`
}

// PluginInstallRequest 插件安装请求
type PluginInstallRequest struct {
	PluginUniqueIdentifiers []string `json:"plugin_unique_identifiers"`
}

// PluginInstallResponse 插件安装响应
type PluginInstallResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// PluginInfoWithProvider 包含provider信息的插件信息
type PluginInfoWithProvider struct {
	ProviderName            string `json:"provider_name"`
	PluginUniqueIdentifier  string `json:"plugin_unique_identifier"`
	PluginName              string `json:"plugin_name"`
	PluginDescription       string `json:"plugin_description"`
	PluginAuthor            string `json:"plugin_author"`
	PluginVersion           string `json:"plugin_version"`
	OriginalProvider        string `json:"original_provider"`
}

// AuthInfo 授权信息
type AuthInfo struct {
	IsTeamAuthorization bool                   `json:"is_team_authorization"`
	TeamCredentials     map[string]interface{} `json:"team_credentials"`
	Type                string                 `json:"type"`
	FullName            string                 `json:"full_name"`
}

// NewDifyApiConfig 创建默认的Dify API配置
func NewDifyApiConfig() *DifyApiConfig {
	return &DifyApiConfig{
		BaseURL:   "http://************",
		ApiKey:    "lYkiYYT6owG+71oLerGzA7GXCgOT++6ovaezWAjpCjf+Sjc3ZtU+qUEi",
		AuthToken: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.HWPgwNIp0ux1ii9AAcM7CSxKXiTQ3I2r63uhd8FWuxI",
		TenantID:  "f24e8fa4-b522-48b2-a5ff-fe639b38d089",
	}
}

// FromConfig 从配置创建DifyApiConfig
func (c *DifyApiConfig) FromConfig(config map[string]interface{}) {
	if baseURL, ok := config["base_url"].(string); ok {
		c.BaseURL = baseURL
	}
	if apiKey, ok := config["api_key"].(string); ok {
		c.ApiKey = apiKey
	}
	if authToken, ok := config["auth_token"].(string); ok {
		c.AuthToken = authToken
	}
	if tenantID, ok := config["tenant_id"].(string); ok {
		c.TenantID = tenantID
	}
}

// Validate 验证配置是否有效
func (c *DifyApiConfig) Validate() error {
	if c.BaseURL == "" {
		return fmt.Errorf("base_url is required")
	}
	if c.ApiKey == "" {
		return fmt.Errorf("api_key is required")
	}
	if c.AuthToken == "" {
		return fmt.Errorf("auth_token is required")
	}
	if c.TenantID == "" {
		return fmt.Errorf("tenant_id is required")
	}
	return nil
}

// ToPluginInfo 将DifyPluginListItem转换为PluginInfo
func (d *DifyPluginListItem) ToPluginInfo() *PluginInfo {
	var authStatus AuthStatus
	switch d.AuthStatus {
	case "no_auth_required":
		authStatus = AuthStatusNotRequired
	case "auth_required", "unauthorized":
		authStatus = AuthStatusRequired
	case "authorized":
		authStatus = AuthStatusAuthorized
	default:
		authStatus = AuthStatusInvalid
	}

	return &PluginInfo{
		PluginID:         d.ProviderName,
		Name:             d.PluginName,
		Version:          d.PluginVersion,
		Description:      d.PluginDescription,
		UniqueIdentifier: d.PluginUniqueID,
		Status:           PluginStatusInstalled,
		AuthStatus:       authStatus,
		InstallTime:      nil, // 需要从其他地方获取
		Permissions:      make(map[string]interface{}),
		Metadata:         make(map[string]interface{}),
	}
}

// GetHeaders 获取Dify API请求头
func (c *DifyApiConfig) GetHeaders() map[string]string {
	return map[string]string{
		"X-Api-Key":      c.ApiKey,
		"Authorization":  c.AuthToken,
		"Content-Type":   "application/json",
	}
}

// GetUploadHeaders 获取文件上传请求头（不包含Content-Type）
func (c *DifyApiConfig) GetUploadHeaders() map[string]string {
	return map[string]string{
		"X-Api-Key":     c.ApiKey,
		"Authorization": c.AuthToken,
	}
}
