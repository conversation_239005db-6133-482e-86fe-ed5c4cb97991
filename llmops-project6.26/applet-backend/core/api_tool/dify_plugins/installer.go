package dify_plugins

import (
	"archive/zip"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"gopkg.in/yaml.v2"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

// Installer Dify插件安装器
type Installer struct {
	config        *DifyApiConfig
	httpClient    *http.Client
	storageDir    string
	pluginsDBFile string
}

// NewInstaller 创建新的插件安装器
func NewInstaller(config *DifyApiConfig) *Installer {
	storageDir := "/tmp/dify_plugins"
	// 确保存储目录存在
	os.MkdirAll(storageDir, 0755)

	return &Installer{
		config:        config,
		httpClient:    &http.Client{Timeout: 60 * time.Second},
		storageDir:    storageDir,
		pluginsDBFile: filepath.Join(storageDir, "plugins.json"),
	}
}

// UploadAndInstall 上传并安装.difypkg文件
func (i *Installer) UploadAndInstall(ctx context.Context, fileContent []byte, filename string) (*PluginUploadResult, error) {
	stdlog.Infof("开始安装插件: %s", filename)

	// 1. 验证文件
	if !strings.HasSuffix(filename, ".difypkg") {
		return &PluginUploadResult{
			Success: false,
			Message: "文件格式不正确，必须是.difypkg文件",
		}, nil
	}

	// 2. 提取插件清单
	manifest, err := i.extractPluginManifest(fileContent)
	if err != nil {
		return &PluginUploadResult{
			Success: false,
			Message: fmt.Sprintf("无法读取插件清单: %v", err),
		}, nil
	}

	// 3. 上传到Dify
	uploadSuccess, uploadMessage, uniqueIdentifier, err := i.uploadToDify(ctx, fileContent, filename)
	if err != nil {
		return &PluginUploadResult{
			Success: false,
			Message: fmt.Sprintf("上传失败: %v", err),
		}, nil
	}
	if !uploadSuccess {
		return &PluginUploadResult{
			Success: false,
			Message: fmt.Sprintf("上传失败: %s", uploadMessage),
		}, nil
	}

	// 4. 安装插件
	installSuccess, installMessage, err := i.installPlugin(ctx, uniqueIdentifier)
	if err != nil {
		return &PluginUploadResult{
			Success: false,
			Message: fmt.Sprintf("安装失败: %v", err),
		}, nil
	}
	if !installSuccess {
		return &PluginUploadResult{
			Success: false,
			Message: fmt.Sprintf("安装失败: %s", installMessage),
		}, nil
	}

	// 5. 创建插件信息
	pluginID := getStringFromMap(manifest, "name")
	if pluginID == "" {
		pluginID = uniqueIdentifier
	}

	authRequired := i.checkAuthRequired(manifest)

	pluginInfo := &PluginInfo{
		PluginID:         pluginID,
		Name:             getStringFromMap(manifest, "label", getStringFromMap(manifest, "name", "Unknown Plugin")),
		Version:          getStringFromMap(manifest, "version", "1.0.0"),
		Author:           getStringFromMap(manifest, "author", "Unknown"),
		Description:      getStringFromMap(manifest, "description", ""),
		UniqueIdentifier: uniqueIdentifier,
		Status:           PluginStatusInstalled,
		AuthStatus: func() AuthStatus {
			if authRequired {
				return AuthStatusRequired
			}
			return AuthStatusNotRequired
		}(),
		InstallTime: func() *time.Time {
			now := time.Now()
			return &now
		}(),
		Permissions: make(map[string]interface{}),
		Metadata:    map[string]interface{}{"manifest": manifest},
	}

	// 6. 保存到数据库
	if err := i.savePluginToDB(pluginID, pluginInfo); err != nil {
		stdlog.Warnf("保存插件到数据库失败: %v", err)
	}

	// 7. 保存插件文件副本
	pluginFileDest := filepath.Join(i.storageDir, fmt.Sprintf("%s.difypkg", pluginID))
	if err := os.WriteFile(pluginFileDest, fileContent, 0644); err != nil {
		stdlog.Warnf("保存插件文件副本失败: %v", err)
	}

	// 根据安装消息确定最终消息
	finalMessage := "插件安装成功"
	if strings.Contains(installMessage, "已存在") {
		finalMessage = "插件已存在，更新成功"
	}

	return &PluginUploadResult{
		Success:    true,
		Message:    finalMessage,
		PluginInfo: pluginInfo,
	}, nil
}

// extractPluginManifest 从.difypkg文件中提取插件清单
func (i *Installer) extractPluginManifest(fileContent []byte) (map[string]interface{}, error) {
	reader := bytes.NewReader(fileContent)
	zipReader, err := zip.NewReader(reader, int64(len(fileContent)))
	if err != nil {
		return nil, fmt.Errorf("无法读取zip文件: %v", err)
	}

	// 查找manifest文件
	var manifestFiles []string
	for _, file := range zipReader.File {
		if strings.HasSuffix(file.Name, "manifest.yaml") ||
			strings.HasSuffix(file.Name, "manifest.yml") ||
			strings.HasSuffix(file.Name, "manifest.json") {
			manifestFiles = append(manifestFiles, file.Name)
		}
	}

	if len(manifestFiles) == 0 {
		return nil, fmt.Errorf("未找到插件清单文件")
	}

	// 读取第一个找到的清单文件
	manifestFile := manifestFiles[0]
	var manifestContent []byte

	for _, file := range zipReader.File {
		if file.Name == manifestFile {
			rc, err := file.Open()
			if err != nil {
				return nil, fmt.Errorf("无法打开清单文件: %v", err)
			}
			defer rc.Close()

			manifestContent, err = io.ReadAll(rc)
			if err != nil {
				return nil, fmt.Errorf("无法读取清单文件: %v", err)
			}
			break
		}
	}

	if len(manifestContent) == 0 {
		return nil, fmt.Errorf("清单文件为空")
	}

	var manifest map[string]interface{}
	if strings.HasSuffix(manifestFile, ".json") {
		if err := json.Unmarshal(manifestContent, &manifest); err != nil {
			return nil, fmt.Errorf("解析JSON清单失败: %v", err)
		}
	} else if strings.HasSuffix(manifestFile, ".yaml") || strings.HasSuffix(manifestFile, ".yml") {
		if err := yaml.Unmarshal(manifestContent, &manifest); err != nil {
			return nil, fmt.Errorf("解析YAML清单失败: %v", err)
		}
	} else {
		return nil, fmt.Errorf("不支持的清单文件格式: %s", manifestFile)
	}

	return manifest, nil
}

// uploadToDify 上传.difypkg文件到Dify
func (i *Installer) uploadToDify(ctx context.Context, fileContent []byte, filename string) (bool, string, string, error) {
	url := fmt.Sprintf("%s/console/api/workspaces/current/plugin/upload/pkg", i.config.BaseURL)

	// 创建multipart form
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加文件字段
	part, err := writer.CreateFormFile("pkg", filename)
	if err != nil {
		return false, "", "", fmt.Errorf("创建form文件字段失败: %v", err)
	}

	if _, err := part.Write(fileContent); err != nil {
		return false, "", "", fmt.Errorf("写入文件内容失败: %v", err)
	}

	if err := writer.Close(); err != nil {
		return false, "", "", fmt.Errorf("关闭multipart writer失败: %v", err)
	}

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "POST", url, &buf)
	if err != nil {
		return false, "", "", fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	for key, value := range i.config.GetUploadHeaders() {
		req.Header.Set(key, value)
	}
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 发送请求
	resp, err := i.httpClient.Do(req)
	if err != nil {
		return false, "", "", fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, "", "", fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode == http.StatusOK {
		var result map[string]interface{}
		if err := json.Unmarshal(body, &result); err != nil {
			return false, "", "", fmt.Errorf("解析响应失败: %v", err)
		}

		// 从响应中提取unique_identifier
		uniqueIdentifier := ""
		if uid, ok := result["unique_identifier"].(string); ok {
			uniqueIdentifier = uid
		} else if plugin, ok := result["plugin"].(map[string]interface{}); ok {
			if name, ok := plugin["name"].(string); ok {
				if version, ok := plugin["version"].(string); ok {
					uniqueIdentifier = fmt.Sprintf("%s:%s", name, version)
				}
			}
		}

		return true, "上传成功", uniqueIdentifier, nil
	} else {
		errorMsg := fmt.Sprintf("上传失败: HTTP %d", resp.StatusCode)
		var errorDetail map[string]interface{}
		if json.Unmarshal(body, &errorDetail) == nil {
			if msg, ok := errorDetail["message"].(string); ok {
				errorMsg += fmt.Sprintf(" - %s", msg)
			}
		} else {
			errorMsg += fmt.Sprintf(" - %s", string(body))
		}
		return false, errorMsg, "", nil
	}
}

// installPlugin 通过unique_identifier安装插件到Dify
func (i *Installer) installPlugin(ctx context.Context, uniqueIdentifier string) (bool, string, error) {
	url := fmt.Sprintf("%s/console/api/workspaces/current/plugin/install/pkg", i.config.BaseURL)

	payload := PluginInstallRequest{
		PluginUniqueIdentifiers: []string{uniqueIdentifier},
	}

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return false, "", fmt.Errorf("序列化请求失败: %v", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(payloadBytes))
	if err != nil {
		return false, "", fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	for key, value := range i.config.GetHeaders() {
		req.Header.Set(key, value)
	}

	resp, err := i.httpClient.Do(req)
	if err != nil {
		return false, "", fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, "", fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode == http.StatusOK {
		return true, "安装成功", nil
	} else if resp.StatusCode == http.StatusBadRequest {
		// 处理插件已存在的情况
		var errorDetail map[string]interface{}
		if json.Unmarshal(body, &errorDetail) == nil {
			if msg, ok := errorDetail["message"].(string); ok {
				if strings.Contains(strings.ToLower(msg), "already installed") {
					stdlog.Warnf("插件已存在: %s", msg)
					return true, "插件已存在，跳过安装", nil
				}
				return false, fmt.Sprintf("安装失败: HTTP %d - %s", resp.StatusCode, msg), nil
			}
		}

		errorText := string(body)
		if strings.Contains(strings.ToLower(errorText), "already installed") {
			stdlog.Warnf("插件已存在: %s", errorText)
			return true, "插件已存在，跳过安装", nil
		}
		return false, fmt.Sprintf("安装失败: HTTP %d - %s", resp.StatusCode, errorText), nil
	} else {
		errorMsg := fmt.Sprintf("安装失败: HTTP %d", resp.StatusCode)
		var errorDetail map[string]interface{}
		if json.Unmarshal(body, &errorDetail) == nil {
			if msg, ok := errorDetail["message"].(string); ok {
				errorMsg += fmt.Sprintf(" - %s", msg)
			}
		} else {
			errorMsg += fmt.Sprintf(" - %s", string(body))
		}
		return false, errorMsg, nil
	}
}

// checkAuthRequired 检查插件是否需要授权
func (i *Installer) checkAuthRequired(manifest map[string]interface{}) bool {
	// 检查Tool Provider的credentials_schema
	if tool, ok := manifest["tool"].(map[string]interface{}); ok {
		if credentialsSchema, ok := tool["credentials_schema"].([]interface{}); ok {
			if len(credentialsSchema) > 0 {
				stdlog.Debugf("插件需要授权，credentials_schema: %d 个字段", len(credentialsSchema))
				return true
			}
		}
	}

	// 检查Model Provider的credentials_schema
	if model, ok := manifest["model"].(map[string]interface{}); ok {
		if credentialsSchema, ok := model["credentials_schema"].([]interface{}); ok {
			if len(credentialsSchema) > 0 {
				stdlog.Debugf("模型插件需要授权，credentials_schema: %d 个字段", len(credentialsSchema))
				return true
			}
		}
	}

	// 检查Agent Strategy Provider的credentials_schema
	if agentStrategy, ok := manifest["agent_strategy"].(map[string]interface{}); ok {
		if credentialsSchema, ok := agentStrategy["credentials_schema"].([]interface{}); ok {
			if len(credentialsSchema) > 0 {
				stdlog.Debugf("智能体策略插件需要授权，credentials_schema: %d 个字段", len(credentialsSchema))
				return true
			}
		}
	}

	stdlog.Debugf("插件不需要授权")
	return false
}

// savePluginToDB 保存插件信息到数据库
func (i *Installer) savePluginToDB(pluginID string, pluginInfo *PluginInfo) error {
	pluginsDB, err := i.loadPluginsDB()
	if err != nil {
		return fmt.Errorf("加载插件数据库失败: %v", err)
	}

	pluginsDB[pluginID] = pluginInfo
	return i.savePluginsDB(pluginsDB)
}

// loadPluginsDB 加载插件数据库
func (i *Installer) loadPluginsDB() (map[string]*PluginInfo, error) {
	if _, err := os.Stat(i.pluginsDBFile); os.IsNotExist(err) {
		return make(map[string]*PluginInfo), nil
	}

	data, err := os.ReadFile(i.pluginsDBFile)
	if err != nil {
		return nil, fmt.Errorf("读取插件数据库文件失败: %v", err)
	}

	var rawData map[string]map[string]interface{}
	if err := json.Unmarshal(data, &rawData); err != nil {
		return nil, fmt.Errorf("解析插件数据库失败: %v", err)
	}

	plugins := make(map[string]*PluginInfo)
	for pluginID, pluginData := range rawData {
		// 转换状态枚举
		status := PluginStatusInstalled
		if statusStr, ok := pluginData["status"].(string); ok {
			status = PluginStatus(statusStr)
		}

		authStatus := AuthStatusNotRequired
		if authStatusStr, ok := pluginData["auth_status"].(string); ok {
			authStatus = AuthStatus(authStatusStr)
		}

		// 转换时间
		var installTime *time.Time
		if installTimeStr, ok := pluginData["install_time"].(string); ok && installTimeStr != "" {
			if t, err := time.Parse(time.RFC3339, installTimeStr); err == nil {
				installTime = &t
			}
		}

		plugin := &PluginInfo{
			PluginID:         getStringFromInterface(pluginData["plugin_id"]),
			Name:             getStringFromInterface(pluginData["name"]),
			Version:          getStringFromInterface(pluginData["version"]),
			Author:           getStringFromInterface(pluginData["author"]),
			Description:      getStringFromInterface(pluginData["description"]),
			UniqueIdentifier: getStringFromInterface(pluginData["unique_identifier"]),
			Status:           status,
			AuthStatus:       authStatus,
			InstallTime:      installTime,
			Permissions:      getMapFromInterface(pluginData["permissions"]),
			Metadata:         getMapFromInterface(pluginData["metadata"]),
		}

		plugins[pluginID] = plugin
	}

	return plugins, nil
}

// savePluginsDB 保存插件数据库
func (i *Installer) savePluginsDB(plugins map[string]*PluginInfo) error {
	data := make(map[string]map[string]interface{})
	for pluginID, pluginInfo := range plugins {
		pluginDict := map[string]interface{}{
			"plugin_id":         pluginInfo.PluginID,
			"name":              pluginInfo.Name,
			"version":           pluginInfo.Version,
			"author":            pluginInfo.Author,
			"description":       pluginInfo.Description,
			"unique_identifier": pluginInfo.UniqueIdentifier,
			"status":            string(pluginInfo.Status),
			"auth_status":       string(pluginInfo.AuthStatus),
			"permissions":       pluginInfo.Permissions,
			"metadata":          pluginInfo.Metadata,
		}

		if pluginInfo.InstallTime != nil {
			pluginDict["install_time"] = pluginInfo.InstallTime.Format(time.RFC3339)
		}

		data[pluginID] = pluginDict
	}

	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化插件数据失败: %v", err)
	}

	return os.WriteFile(i.pluginsDBFile, jsonData, 0644)
}

// 辅助函数
func getStringFromMap(m map[string]interface{}, keys ...string) string {
	for _, key := range keys {
		if val, ok := m[key]; ok {
			if str, ok := val.(string); ok {
				return str
			}
		}
	}
	return ""
}

func getStringFromInterface(val interface{}) string {
	if str, ok := val.(string); ok {
		return str
	}
	return ""
}

func getMapFromInterface(val interface{}) map[string]interface{} {
	if m, ok := val.(map[string]interface{}); ok {
		return m
	}
	return make(map[string]interface{})
}
