package api_tool

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.com/aws/smithy-go/ptr"
	"github.com/getkin/kin-openapi/openapi3"
	"github.com/google/uuid"
	"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/core/api_tool/dify_plugins"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/models/api_tools"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

var (
	ToolManager    IAPITool
	dbOnce         sync.Once
	BuiltinToolsZh []*api_tools.APIToolCollectionDO
	BuiltinToolsEn []*api_tools.APIToolCollectionDO
)

const (
	ParserTypeYaml     = "yaml"
	ParserTypeJson     = "json"
	CollectionIDPrefix = "builtin-collection-"
	ToolIDPrefix       = "builtin-tool-"
	AppletBackendSvc   = "autocv-applet-service"
)

func InitTools(ctx context.Context) error {
	ParserMap = make(map[string]APIMetaParser)
	ParserMap[ParserTypeYaml] = YamlParser{}
	ParserMap[ParserTypeJson] = JsonParser{}
	ToolManager = &APITool{}
	if err := InitToolTemplate(ctx); err != nil {
		return err
	}

	// 初始化Dify插件管理器
	if err := InitDifyPluginManager(ctx); err != nil {
		stdlog.Errorf("初始化Dify插件管理器失败: %v", err)
		// 不返回错误，允许系统继续运行
	}

	BuiltinToolsZh = make([]*api_tools.APIToolCollectionDO, 0)
	BuiltinToolsEn = make([]*api_tools.APIToolCollectionDO, 0)
	if err := InitBuiltinTools("zh"); err != nil {
		return err
	}
	if err := InitBuiltinTools("en"); err != nil {
		return err
	}
	return nil
}

// InitDifyPluginManager 初始化Dify插件管理器
func InitDifyPluginManager(ctx context.Context) error {
	// 创建默认配置
	config := dify_plugins.NewDifyApiConfig()

	// 这里可以从配置文件中读取配置
	// 暂时使用默认配置

	// 验证配置
	if err := config.Validate(); err != nil {
		return stderr.Wrap(err, "Dify配置验证失败")
	}

	// 初始化全局管理器
	dify_plugins.InitGlobalManager(config)

	stdlog.Infof("Dify插件管理器初始化成功")
	return nil
}

func InitToolTemplate(ctx context.Context) error {
	//if err := dao.APIToolCollectionDemoImpl.Upsert(ctx, &generated.APIToolCollectionDemo{
	//	ID:        uuid.New().String(),
	//	Name:      "宠物接口示例",
	//	MetaType:  "yaml",
	//	MetaInfo:  []byte(ToolDemoPet),
	//	Desc:      "宠物接口测试API",
	//	ProjectID: "",
	//}); err != nil {
	//	return err
	//}
	if err := dao.APIToolCollectionDemoImpl.Upsert(ctx, &generated.APIToolCollectionDemo{
		ID:        uuid.New().String(),
		Name:      "API Test",
		MetaType:  "yaml",
		MetaInfo:  []byte(ToolDemoTest),
		Desc:      "API Test",
		ProjectID: "",
	}); err != nil {
		return err
	}
	return nil
}

func InitBuiltinTools(lang string) error {
	if conf.Config.IsSimpleMode {
		return nil
	}
	currentNamespace := k8s.CurrentNamespaceInCluster()
	builtinOpenapiUrl := strings.ReplaceAll(conf.Config.APIToolConfig.BuiltinOpenapi.Url, conf.NamespacePlaceholder, currentNamespace)
	stdlog.Infof("builtin openapi url: %s", builtinOpenapiUrl)
	builtinOpenapiUrl, err := appendQueryToURL(builtinOpenapiUrl, "lang", lang)
	resp, err := http.Get(builtinOpenapiUrl)
	if err != nil {
		return stderr.Wrap(err, "failed to fetch openapis from URL %s", builtinOpenapiUrl)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return stderr.Error("failed to fetch openapis, status code: %d", resp.StatusCode)
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return stderr.Wrap(err, "failed to read response body")
	}

	var openapiList []*openapi3.T
	if err := json.Unmarshal(data, &openapiList); err != nil {
		return stderr.Wrap(err, "failed to unmarshal openapi list")
	}

	toolApiKeys := map[string]string{
		"高德":              "9e072949c5d5eb0ffc463ed8d81d4f8c",
		"gaode":           "9e072949c5d5eb0ffc463ed8d81d4f8c",
		"必应搜索":            "********************************",
		"Bing Search API": "********************************",
		"github仓库":        "*********************************************************************************************",
		"github":          "*********************************************************************************************",
		"网页爬取":            "fc-40cc28cee8594c569c59b7abf4e0de2d",
		"firecrawl":       "fc-40cc28cee8594c569c59b7abf4e0de2d",
		"企业微信":            "b2a8174d-9e4e-4954-8ad8-39838884aeda",
		"wecom":           "b2a8174d-9e4e-4954-8ad8-39838884aeda",
		"钉钉":              "ba87c6eed6b6d7fb2aa85b0a5dda75014c4908ee378762e2d314b375162835a4",
		"dingtalk":        "ba87c6eed6b6d7fb2aa85b0a5dda75014c4908ee378762e2d314b375162835a4",
	}

	allToolsSwitch := conf.Config.APIToolConfig.BuiltinAllToolsSwitch
	for _, openapi := range openapiList {
		parser := &api_tools.OpenAPI3Parser{Spec: *openapi}
		apis, err := parser.GetAPIs()
		if err != nil {
			// 修改打印的日志，GetAPIs函数报错
			stdlog.Errorf("GetAPIs of openai parse failed: %+v", err)
			continue
		}
		openapiBytes, err := json.MarshalIndent(openapi, "", "  ")
		if err != nil {
			stdlog.Errorf("json marshal openai %+v failed: %+v", openapi, err)
			continue
		}
		// 为每个api生成id
		for _, api := range apis {
			api.ID = genToolID(api.Name)
		}
		avatarUrl := parser.GetAvatarUrl()
		baseUrl := strings.ReplaceAll(parser.GetBaseURL(), conf.NamespacePlaceholder, currentNamespace)
		nowSec := time.Now().Unix()
		toolCollection := &api_tools.APIToolCollectionDO{
			BaseInfo: api_tools.APIToolCollectionBaseDO{
				ID:                 genCollectionID(parser.GetAPIName()),
				Name:               parser.GetAPIName(),
				Desc:               parser.GetDesc(),
				APIToolCnt:         int64(len(apis)),
				Released:           ptr.Bool(true),
				CreateTimeSec:      nowSec,
				UpdateTimeSec:      nowSec,
				LastReleaseTimeSec: time.Now().Unix(),
				Type:               api_tools.APIToolTypeBuiltin,
				LogoUrl:            avatarUrl,
				ServerType:         agent_definition.ServerTypeRest,
			},
			Tools:    apis,
			MetaType: api_tools.APIToolMetaTypeJson,
			MetaInfo: openapiBytes,
			BaseURL:  baseUrl,
		}

		if value, exists := toolApiKeys[parser.GetAPIName()]; exists {
			toolCollection.BaseInfo.ServerType = agent_definition.ServerTypeMCP
			toolCollection.BaseInfo.McpType = agent_definition.McpTypeStreamableHttp
			toolCollection.BaseURL = fmt.Sprintf("http://%s.%s%s/api/v1/tool/collections/%s/dynamic-mcp", AppletBackendSvc, currentNamespace, conf.Config.Server.Addr, toolCollection.BaseInfo.ID)
			for _, t := range toolCollection.Tools {
				t.Path = baseUrl + t.Path
			}
			toolCollection.Params = []*agent_definition.MCPParam{
				{
					Name:           "Api-Key",
					DefaultValue:   value,
					ParamValueType: "string",
					Desc:           "api key",
					Display:        true,
					Encryption:     true,
					Customize:      true,
				},
			}
		}
		// 构建 AgentToolCollection
		toolCollection = genToolCollectionDescriber(toolCollection)

		if lang == "zh" {
			// 判断是否保留
			if _, exists := toolApiKeys[parser.GetAPIName()]; exists || parser.GetUsedInProduction() || allToolsSwitch {
				BuiltinToolsZh = append(BuiltinToolsZh, toolCollection)
			}
		}
		if lang == "en" {
			if _, exists := toolApiKeys[parser.GetAPIName()]; exists || parser.GetUsedInProduction() || allToolsSwitch {
				BuiltinToolsEn = append(BuiltinToolsEn, toolCollection)
			}
		}
	}

	// add wiki builtin dynamic mcp
	wikiCollection := newWikiMcpServer()
	wikiCollection = genToolCollectionDescriber(wikiCollection)

	// add project knowledge base dynamic mcp
	kbCollection := newKbMcpServer()
	kbCollection = genToolCollectionDescriber(kbCollection)

	if lang == "zh" {
		BuiltinToolsZh = append(BuiltinToolsZh, wikiCollection, kbCollection)
	}
	if lang == "en" {
		BuiltinToolsEn = append(BuiltinToolsEn, wikiCollection, kbCollection)
	}
	return nil
}

func appendQueryToURL(urlStr string, queryParamName string, queryParamValue string) (string, error) {
	u, err := url.Parse(urlStr)
	if err != nil {
		return "", stderr.Wrap(err, "无法解析URL")
	}

	//设置queryParam
	q := u.Query()
	q.Set(queryParamName, queryParamValue)
	u.RawQuery = q.Encode()

	return u.String(), err
}

func genCollectionID(name string) string {
	return CollectionIDPrefix + string2ID(name)
}

func genToolID(name string) string {
	return ToolIDPrefix + string2ID(name)
}

func string2ID(s string) string {
	hash := md5.Sum([]byte(s))
	return uuid.NewMD5(uuid.Nil, hash[:]).String()[:16]
}

func genToolCollectionDescriber(c *api_tools.APIToolCollectionDO) *api_tools.APIToolCollectionDO {
	// 构建 AgentToolCollection
	agentTools := make([]agent_definition.APIToolDescriber, 0, len(c.Tools))
	data, err := json.Marshal(c.Params)
	if err != nil {
		return c
	}
	var connParams []agent_definition.MCPParam
	json.Unmarshal(data, &connParams)
	for _, t := range c.Tools {
		agentTools = append(agentTools, agent_definition.APIToolDescriber{
			ID:                t.ID,
			BaseURL:           c.BaseURL,
			Method:            t.Method,
			CollectionHeaders: make(map[string]string), // 如果有全局header，可以在这里设置
			APIPath:           t.Path,
			Name:              t.Name,
			Desc:              t.Desc,
			Params:            t.ParamValues,
			CollectionId:      c.BaseInfo.ID,
			CollectionName:    c.BaseInfo.Name,
			ServerType:        c.BaseInfo.ServerType,
			McpType:           c.BaseInfo.McpType,
			CollectionParams:  connParams,
		})
	}

	c.AgentToolCollection = &agent_definition.APIToolCollectionDescriber{
		ID:         c.BaseInfo.ID,
		Name:       c.BaseInfo.Name,
		Desc:       c.BaseInfo.Desc,
		AgentTools: agentTools,
	}
	return c
}
