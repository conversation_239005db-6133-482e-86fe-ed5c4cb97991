#!/usr/bin/env python3
"""
测试Dify插件迁移后的功能
"""

import requests
import json
import time
import os

def test_applet_backend_dify_plugins():
    """测试applet-backend的dify插件功能"""
    print("🔍 测试applet-backend的dify插件功能...")
    
    base_url = "http://localhost:8081/api/v1/tool"
    
    # 测试1: 获取已安装的dify插件
    print("\n1. 测试获取已安装的dify插件...")
    try:
        url = f"{base_url}/dify-plugins:installed"
        response = requests.get(url, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 获取dify插件成功")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if isinstance(data, list):
                print(f"📊 获取到 {len(data)} 个dify插件")
                for plugin in data[:3]:  # 显示前3个
                    print(f"- {plugin.get('plugin_name', 'Unknown')}: {plugin.get('auth_status', 'unknown')}")
            else:
                print(f"⚠️ 响应格式异常: {type(data)}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def test_tool_collections():
    """测试工具集合API是否包含dify插件"""
    print("\n2. 测试工具集合API...")
    
    try:
        url = "http://localhost:8081/api/v1/tool/collections"
        response = requests.get(url, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 获取工具集合成功")
            
            # 统计不同类型的工具
            tool_stats = {
                'rest': 0,
                'mcp': 0,
                'dynamic_mcp': 0,
                'dify': 0,
                'other': 0
            }
            
            dify_tools = []
            
            for tool in data:
                server_type = tool.get('server_type', 'unknown')
                if server_type == 'dify':
                    tool_stats['dify'] += 1
                    dify_tools.append(tool)
                elif server_type in tool_stats:
                    tool_stats[server_type] += 1
                else:
                    tool_stats['other'] += 1
            
            print(f"📊 工具统计:")
            print(f"- REST工具: {tool_stats['rest']}")
            print(f"- MCP工具: {tool_stats['mcp']}")
            print(f"- 动态MCP工具: {tool_stats['dynamic_mcp']}")
            print(f"- Dify插件: {tool_stats['dify']}")
            print(f"- 其他工具: {tool_stats['other']}")
            
            if dify_tools:
                print(f"\n🔌 Dify插件详情:")
                for tool in dify_tools[:5]:  # 显示前5个
                    print(f"- {tool.get('name', 'Unknown')}: {tool.get('desc', 'No description')}")
            else:
                print("⚠️ 未找到dify插件")
                
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def test_dify_plugin_upload():
    """测试dify插件上传功能"""
    print("\n3. 测试dify插件上传功能...")
    
    # 这里只测试API是否可访问，不实际上传文件
    try:
        url = "http://localhost:8081/api/v1/tool/dify-plugins:upload"
        # 发送一个空的POST请求来测试API是否存在
        response = requests.post(url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 400:
            print("✅ 上传API存在（返回400是因为没有文件）")
        elif response.status_code == 500:
            print("⚠️ 上传API存在但可能有内部错误")
        else:
            print(f"📝 上传API响应: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 上传API测试失败: {e}")

def test_agent_tool_api_removed():
    """测试agent-tool-api中的dify插件功能是否已移除"""
    print("\n4. 测试agent-tool-api中的dify插件功能是否已移除...")
    
    try:
        url = "http://localhost:8080/v1/dify-plugins/installed"
        response = requests.get(url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 404:
            print("✅ agent-tool-api中的dify插件API已成功移除")
        else:
            print(f"⚠️ agent-tool-api中的dify插件API仍然存在: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("⚠️ agent-tool-api服务未运行")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_direct_dify_api():
    """测试直接访问Dify API"""
    print("\n5. 测试直接访问Dify API...")
    
    try:
        url = "http://************/console/api/workspaces/current/plugin/list"
        headers = {
            "X-Api-Key": "lYkiYYT6owG+71oLerGzA7GXCgOT++6ovaezWAjpCjf+Sjc3ZtU+qUEi",
            "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMGI2N2Y3ZjYtMjQ5OC00ZjM0LTk0ZDEtMmMwZmI0NDVmMDg0IiwiZXhwIjoxNzg0OTQ4NzE1LCJpc3MiOiJTRUxGX0hPU1RFRCIsInN1YiI6IkNvbnNvbGUgQVBJIFBhc3Nwb3J0In0.HWPgwNIp0ux1ii9AAcM7CSxKXiTQ3I2r63uhd8FWuxI",
            "Content-Type": "application/json"
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            plugins = data.get('plugins', [])
            print(f"✅ Dify API正常，获取到 {len(plugins)} 个插件")
            
            for plugin in plugins[:3]:  # 显示前3个
                print(f"- {plugin.get('name', 'Unknown')}: {plugin.get('unique_identifier', 'No ID')}")
        else:
            print(f"❌ Dify API错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ Dify API测试失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试Dify插件迁移后的功能")
    print("=" * 60)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(2)
    
    # 执行测试
    test_direct_dify_api()
    test_applet_backend_dify_plugins()
    test_tool_collections()
    test_dify_plugin_upload()
    test_agent_tool_api_removed()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    
    print("\n📋 测试总结:")
    print("1. ✅ Dify API连接正常")
    print("2. 🔄 applet-backend dify插件功能")
    print("3. 🔄 工具集合API包含dify插件")
    print("4. 🔄 dify插件上传API")
    print("5. 🔄 agent-tool-api dify功能移除")
    
    print("\n💡 如果某些测试失败，请检查:")
    print("- applet-backend服务是否正常运行")
    print("- Dify插件管理器是否正确初始化")
    print("- 网络连接是否正常")

if __name__ == "__main__":
    main()
